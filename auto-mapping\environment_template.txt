# Copy this to .env file in the same directory

# Database Configuration
POSTGRES_USER=your_username
POSTGRES_PASSWORD=your_password
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=your_database_name

# AI API Keys (choose one or more based on your needs)
OPENAI_API_KEY=your_openai_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Optional: CrewAI Configuration
CREWAI_TELEMETRY_OPT_OUT=true


# Embeddings directory override (optional). Absolute path recommended.
# Example for Windows:
# EMBEDDINGS_DIR=G:\\Projects\\mcgrocer-project\\category-mapper\\auto-mapping\\embeddings
# Example for macOS/Linux:
# EMBEDDINGS_DIR=/path/to/category-mapper/auto-mapping/embeddings
