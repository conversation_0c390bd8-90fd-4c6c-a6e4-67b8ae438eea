"""
Category Embeddings Manager

This module handles the generation, storage, and retrieval of embeddings
for the category hierarchy. Embeddings are stored locally for fast access.
"""

import os
import json
import numpy as np
import hashlib
from typing import List, Dict, Optional, Tuple
from datetime import datetime
from dotenv import load_dotenv
import psycopg2
from psycopg2.extras import RealDictCursor

# Try to import optional dependencies
try:
    import openai
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    print("Warning: OpenAI not installed. Install with: pip install openai")

try:
    import faiss
    FAISS_AVAILABLE = True
except ImportError:
    FAISS_AVAILABLE = False
    print("Warning: FAISS not installed. Install with: pip install faiss-cpu")

load_dotenv()


class CategoryEmbeddingsManager:
    """Manages embeddings for category hierarchy."""
    
    def __init__(self, embeddings_dir: str = "./embeddings"):
        """
        Initialize the embeddings manager.

        Args:
            embeddings_dir: Directory to store embeddings
        """
        # Resolve embeddings directory with environment override and robust default
        env_dir = os.getenv("EMBEDDINGS_DIR")
        if env_dir and env_dir.strip():
            embeddings_dir = env_dir.strip()
        elif embeddings_dir == "./embeddings":
            # Default to auto-mapping/embeddings relative to this file
            base_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", ".."))
            embeddings_dir = os.path.join(base_dir, "embeddings")

        self.embeddings_dir = embeddings_dir
        os.makedirs(self.embeddings_dir, exist_ok=True)
        print(f"Embeddings directory: {self.embeddings_dir}")

        # File paths
        self.embeddings_file = os.path.join(self.embeddings_dir, "category_embeddings.npz")
        self.metadata_file = os.path.join(self.embeddings_dir, "category_metadata.json")
        self.index_file = os.path.join(self.embeddings_dir, "embeddings.faiss")
        self.config_file = os.path.join(self.embeddings_dir, "config.json")
        
        # Initialize OpenAI client if available
        self.client = None
        if OPENAI_AVAILABLE and os.getenv('OPENAI_API_KEY'):
            self.client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
            print("OpenAI client initialized for embeddings")
        
        # Load existing embeddings if available
        self.embeddings = None
        self.metadata = None
        self.index = None
        self.config = None
        
        self.load_embeddings()
    
    def load_categories_from_db(self) -> List[Dict]:
        """Load unique category paths from database."""
        print("Loading categories from database...")
        
        try:
            conn = psycopg2.connect(
                host=os.getenv('POSTGRES_HOST', 'localhost'),
                port=os.getenv('POSTGRES_PORT', '5433'),
                database=os.getenv('POSTGRES_DB', 'aicategorymapping'),
                user=os.getenv('POSTGRES_USER', 'cat_manager'),
                password=os.getenv('POSTGRES_PASSWORD')
            )
            
            cursor = conn.cursor(cursor_factory=RealDictCursor)
            
            # Get unique category combinations
            sql = """
                SELECT DISTINCT 
                    level_1, level_2, level_3, level_4, level_5, level_6, level_7,
                    COUNT(*) as product_count
                FROM category_output
                WHERE level_1 IS NOT NULL
                GROUP BY level_1, level_2, level_3, level_4, level_5, level_6, level_7
                ORDER BY product_count DESC
            """
            
            cursor.execute(sql)
            results = cursor.fetchall()
            
            categories = []
            for row in results:
                # Build category path
                path_parts = []
                for i in range(1, 8):
                    level = row.get(f'level_{i}')
                    if level and level.strip():
                        path_parts.append(level.strip())
                
                if path_parts:
                    category = {
                        'path': ' > '.join(path_parts),
                        'levels': {f'level_{i}': row.get(f'level_{i}') for i in range(1, 8)},
                        'product_count': row['product_count']
                    }
                    categories.append(category)
            
            cursor.close()
            conn.close()
            
            print(f"Loaded {len(categories)} unique category paths")
            return categories
            
        except Exception as e:
            print(f"Error loading categories from database: {e}")
            return []
    
    def generate_embedding(self, text: str) -> Optional[List[float]]:
        """
        Generate embedding for a single text using OpenAI API.
        
        Args:
            text: Text to embed
            
        Returns:
            Embedding vector or None if failed
        """
        if not self.client:
            print("OpenAI client not available")
            return None
        
        try:
            response = self.client.embeddings.create(
                model="text-embedding-3-small",  # Cheaper and faster
                input=text,
                encoding_format="float"
            )
            return response.data[0].embedding
        except Exception as e:
            print(f"Error generating embedding: {e}")
            return None
    
    def generate_all_embeddings(self, force_regenerate: bool = False):
        """
        Generate embeddings for all categories.
        
        Args:
            force_regenerate: Regenerate even if embeddings exist
        """
        # Check if embeddings already exist
        if not force_regenerate and os.path.exists(self.embeddings_file):
            print("Embeddings already exist. Use force_regenerate=True to regenerate.")
            return
        
        if not self.client:
            print("Error: OpenAI API key not configured. Set OPENAI_API_KEY in .env")
            return
        
        # Load categories from database
        categories = self.load_categories_from_db()
        if not categories:
            print("No categories loaded from database")
            return
        
        print(f"Generating embeddings for {len(categories)} categories...")
        
        embeddings_list = []
        metadata_list = []
        
        # Process in batches for efficiency
        batch_size = 100
        for i in range(0, len(categories), batch_size):
            batch = categories[i:i+batch_size]
            batch_texts = [cat['path'] for cat in batch]
            
            print(f"Processing batch {i//batch_size + 1}/{(len(categories) + batch_size - 1)//batch_size}")
            
            try:
                # Generate embeddings for batch
                response = self.client.embeddings.create(
                    model="text-embedding-3-small",
                    input=batch_texts,
                    encoding_format="float"
                )
                
                for j, embedding_data in enumerate(response.data):
                    embeddings_list.append(embedding_data.embedding)
                    metadata_list.append(batch[j])
                    
            except Exception as e:
                print(f"Error in batch {i//batch_size + 1}: {e}")
                # Add None embeddings for failed items
                for cat in batch:
                    embeddings_list.append(None)
                    metadata_list.append(cat)
        
        # Filter out failed embeddings
        valid_indices = [i for i, e in enumerate(embeddings_list) if e is not None]
        embeddings_array = np.array([embeddings_list[i] for i in valid_indices])
        valid_metadata = [metadata_list[i] for i in valid_indices]
        
        print(f"Successfully generated {len(valid_indices)} embeddings")
        
        # Save embeddings
        self.save_embeddings(embeddings_array, valid_metadata)
        
        # Create FAISS index
        if FAISS_AVAILABLE:
            self.create_faiss_index(embeddings_array)
    
    def save_embeddings(self, embeddings: np.ndarray, metadata: List[Dict]):
        """Save embeddings and metadata to disk."""
        # Save embeddings as compressed numpy array
        np.savez_compressed(self.embeddings_file, embeddings=embeddings)
        print(f"Saved embeddings to {self.embeddings_file}")
        
        # Save metadata as JSON
        with open(self.metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2)
        print(f"Saved metadata to {self.metadata_file}")
        
        # Save configuration
        config = {
            'embedding_model': 'text-embedding-3-small',
            'embedding_dim': embeddings.shape[1] if len(embeddings) > 0 else 1536,
            'num_categories': len(metadata),
            'generated_at': datetime.now().isoformat(),
            'openai_api_version': '1.0'
        }
        with open(self.config_file, 'w') as f:
            json.dump(config, f, indent=2)
        print(f"Saved config to {self.config_file}")
    
    def load_embeddings(self) -> bool:
        """
        Load embeddings from disk.
        
        Returns:
            True if loaded successfully, False otherwise
        """
        if not os.path.exists(self.embeddings_file):
            print("No embeddings found. Run generate_all_embeddings() first.")
            return False
        
        try:
            # Load embeddings
            data = np.load(self.embeddings_file)
            self.embeddings = data['embeddings']
            print(f"Loaded {len(self.embeddings)} embeddings")
            
            # Load metadata
            with open(self.metadata_file, 'r') as f:
                self.metadata = json.load(f)
            
            # Load config
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    self.config = json.load(f)
            
            # Load FAISS index if available
            if FAISS_AVAILABLE and os.path.exists(self.index_file):
                self.index = faiss.read_index(self.index_file)
                print("Loaded FAISS index")
            
            return True
            
        except Exception as e:
            print(f"Error loading embeddings: {e}")
            return False
    
    def create_faiss_index(self, embeddings: Optional[np.ndarray] = None):
        """Create FAISS index for fast similarity search."""
        if not FAISS_AVAILABLE:
            print("FAISS not available. Install with: pip install faiss-cpu")
            return
        
        if embeddings is None:
            embeddings = self.embeddings
        
        if embeddings is None or len(embeddings) == 0:
            print("No embeddings available to index")
            return
        
        print("Creating FAISS index...")
        
        # Convert to float32 and ensure C-contiguous for FAISS
        embeddings_float32 = np.ascontiguousarray(embeddings, dtype='float32')
        
        # Normalize embeddings for cosine similarity
        faiss.normalize_L2(embeddings_float32)
        
        # Create index
        dimension = embeddings_float32.shape[1]
        index = faiss.IndexFlatIP(dimension)  # Inner product (cosine similarity after normalization)
        
        # Add embeddings to index
        index.add(embeddings_float32)
        
        # Save index
        faiss.write_index(index, self.index_file)
        self.index = index
        
        print(f"Created FAISS index with {index.ntotal} vectors")
    
    def search_similar(self, query: str, k: int = 5) -> List[Tuple[Dict, float]]:
        """
        Search for similar categories using embeddings.
        
        Args:
            query: Search query
            k: Number of results to return
            
        Returns:
            List of (category_metadata, similarity_score) tuples
        """
        if self.embeddings is None:
            print("No embeddings loaded")
            return []
        
        # Generate query embedding
        query_embedding = self.generate_embedding(query)
        if query_embedding is None:
            print("Failed to generate query embedding")
            return []
        
        query_vec = np.ascontiguousarray([query_embedding], dtype='float32')
        
        if FAISS_AVAILABLE and self.index is not None:
            # Use FAISS for fast search
            faiss.normalize_L2(query_vec)
            distances, indices = self.index.search(query_vec, k)
            
            results = []
            for i, idx in enumerate(indices[0]):
                if idx >= 0:  # Valid index
                    results.append((
                        self.metadata[idx],
                        float(distances[0][i])  # Cosine similarity
                    ))
            return results
        else:
            # Fallback to numpy cosine similarity
            query_vec = query_vec[0]
            query_norm = query_vec / np.linalg.norm(query_vec)
            
            # Compute similarities
            embeddings_norm = self.embeddings / np.linalg.norm(self.embeddings, axis=1, keepdims=True)
            similarities = np.dot(embeddings_norm, query_norm)
            
            # Get top k
            top_indices = np.argsort(similarities)[-k:][::-1]
            
            results = []
            for idx in top_indices:
                results.append((
                    self.metadata[idx],
                    float(similarities[idx])
                ))
            return results
    
    def get_statistics(self) -> Dict:
        """Get statistics about the embeddings."""
        stats = {
            'embeddings_exist': os.path.exists(self.embeddings_file),
            'num_embeddings': len(self.embeddings) if self.embeddings is not None else 0,
            'index_exists': self.index is not None,
            'config': self.config,
            'storage_size_mb': 0
        }
        
        # Calculate storage size
        if os.path.exists(self.embeddings_dir):
            total_size = 0
            for file in os.listdir(self.embeddings_dir):
                file_path = os.path.join(self.embeddings_dir, file)
                if os.path.isfile(file_path):
                    total_size += os.path.getsize(file_path)
            stats['storage_size_mb'] = round(total_size / (1024 * 1024), 2)
        
        return stats


def test_embeddings_manager():
    """Test the embeddings manager."""
    manager = CategoryEmbeddingsManager()
    
    # Get statistics
    stats = manager.get_statistics()
    print("\nEmbeddings Statistics:")
    print(json.dumps(stats, indent=2))
    
    # Generate embeddings if not exist
    if not stats['embeddings_exist']:
        print("\nNo embeddings found. Would generate with:")
        print("  manager.generate_all_embeddings()")
        print("\nNote: This requires OPENAI_API_KEY in .env")
    else:
        # Test search
        print("\nTesting similarity search...")
        
        test_queries = [
            "garden scoop tool",
            "fresh apple fruit",
            "toothbrush dental"
        ]
        
        for query in test_queries:
            print(f"\nSearching for: '{query}'")
            results = manager.search_similar(query, k=3)
            
            for i, (category, score) in enumerate(results, 1):
                print(f"  {i}. {category['path']}")
                print(f"     Similarity: {score:.3f}")
                print(f"     Products: {category['product_count']}")


if __name__ == "__main__":
    test_embeddings_manager()
