"""
Category Hierarchy Search Tool

This tool searches the CATEGORY HIERARCHY tables (not products!)
to find the best category path for incoming products.
"""

import os
import psycopg2
from psycopg2.extras import RealDictCursor
from typing import Dict, List, Optional, Tuple, Type, Any
from dotenv import load_dotenv
import json
from crewai.tools import BaseTool
from pydantic import BaseModel, Field

load_dotenv()


class CategoryHierarchyTool:
    """
    Tool for searching the category hierarchy to map products.
    This searches CATEGORIES, not products!
    """
    
    def __init__(self):
        """Initialize database connection."""
        self.db_config = {
            'host': os.getenv('POSTGRES_HOST', 'localhost'),
            'port': os.getenv('POSTGRES_PORT', '5433'),
            'database': os.getenv('POSTGRES_DB', 'aicategorymapping'),
            'user': os.getenv('POSTGRES_USER', 'cat_manager'),
            'password': os.getenv('POSTGRES_PASSWORD')
        }
        
        # Cache the category hierarchy on initialization
        self.category_hierarchy = self._load_category_hierarchy()
        print(f"[OK] Loaded category hierarchy: {len(self.category_hierarchy)} complete paths")
    
    def _load_category_hierarchy(self) -> List[Dict]:
        """Load the complete category hierarchy from database."""
        conn = None
        hierarchy = []
        
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor(cursor_factory=RealDictCursor)
            
            # Get all unique category paths from the category_output table
            # This gives us the actual valid category combinations
            sql = """
                SELECT DISTINCT 
                    level_1, level_2, level_3, level_4, level_5, level_6, level_7,
                    COUNT(*) as product_count
                FROM category_output
                WHERE level_1 IS NOT NULL
                GROUP BY level_1, level_2, level_3, level_4, level_5, level_6, level_7
                ORDER BY product_count DESC
            """
            
            cursor.execute(sql)
            results = cursor.fetchall()
            
            for row in results:
                # Build the category path
                path_dict = {
                    'level_1': row['level_1'],
                    'level_2': row['level_2'],
                    'level_3': row['level_3'],
                    'level_4': row['level_4'],
                    'level_5': row['level_5'],
                    'level_6': row['level_6'],
                    'level_7': row['level_7'],
                    'product_count': row['product_count'],
                    'full_path': self._build_path(row)
                }
                hierarchy.append(path_dict)
            
            cursor.close()
            return hierarchy
            
        except Exception as e:
            print(f"[ERROR] Failed to load category hierarchy: {e}")
            return []
        finally:
            if conn:
                conn.close()
    
    def _build_path(self, row: Dict) -> str:
        """Build a readable category path from row data."""
        path = []
        for level in ['level_1', 'level_2', 'level_3', 'level_4', 'level_5', 'level_6', 'level_7']:
            if row.get(level):
                path.append(row[level])
        return ' > '.join(path)
    
    def run(self, query: str) -> str:
        """
        Search the category hierarchy based on query.
        This is the main method CrewAI agents will call.
        
        Args:
            query: Search query describing the product
            
        Returns:
            Formatted string with matching categories
        """
        # Extract keywords from query
        keywords = [w.lower() for w in query.split() if len(w) > 2]
        
        # Find matching categories
        matches = []
        
        for category in self.category_hierarchy:
            score = 0
            matched_keywords = []
            
            # Check each keyword against the category path
            for keyword in keywords:
                full_path_lower = category['full_path'].lower()
                if keyword in full_path_lower:
                    score += 1
                    matched_keywords.append(keyword)
                    
                    # Bonus points for matching specific levels
                    for level in ['level_1', 'level_2', 'level_3', 'level_4', 'level_5']:
                        if category[level] and keyword in category[level].lower():
                            score += 2
            
            if score > 0:
                matches.append({
                    'category': category,
                    'score': score,
                    'matched_keywords': matched_keywords
                })
        
        # Sort by score
        matches.sort(key=lambda x: x['score'], reverse=True)
        
        # Format top matches for agent
        return self._format_matches(matches[:5], keywords)
    
    def _format_matches(self, matches: List[Dict], keywords: List[str]) -> str:
        """Format category matches for agent consumption."""
        if not matches:
            return f"No categories found matching: {' '.join(keywords)}"
        
        output = f"Category search results for: {' '.join(keywords)}\n\n"
        output += "TOP MATCHING CATEGORIES:\n\n"
        
        for i, match in enumerate(matches, 1):
            cat = match['category']
            output += f"{i}. {cat['full_path']}\n"
            output += f"   Matched keywords: {', '.join(match['matched_keywords'])}\n"
            output += f"   Match score: {match['score']}\n"
            output += f"   Products in category: {cat['product_count']:,}\n"
            output += "\n   Category Levels:\n"
            
            for level_num in range(1, 8):
                level_key = f'level_{level_num}'
                if cat[level_key]:
                    output += f"   - {level_key}: {cat[level_key]}\n"
            output += "\n"
        
        return output
    
    def get_all_level_1_categories(self) -> List[str]:
        """Get all unique level 1 categories."""
        unique_level_1 = set()
        for cat in self.category_hierarchy:
            if cat['level_1']:
                unique_level_1.add(cat['level_1'])
        return sorted(list(unique_level_1))
    
    def get_subcategories(self, parent_levels: Dict[str, str]) -> List[Dict]:
        """Get subcategories under given parent levels."""
        matches = []
        for cat in self.category_hierarchy:
            is_match = True
            for level, value in parent_levels.items():
                if cat.get(level) != value:
                    is_match = False
                    break
            if is_match:
                matches.append(cat)
        return matches


def test_category_tool():
    """Test the category hierarchy search tool."""
    print("🧪 Testing Category Hierarchy Tool")
    print("=" * 50)
    
    tool = CategoryHierarchyTool()
    
    # Show level 1 categories
    print("\n📊 Available Level 1 Categories:")
    level_1_cats = tool.get_all_level_1_categories()
    for i, cat in enumerate(level_1_cats, 1):
        print(f"  {i}. {cat}")
    
    print("\n" + "=" * 50)
    
    # Test searches
    test_queries = [
        "garden scoop tool outdoor",
        "fresh apple fruit food",
        "toothbrush dental hygiene personal care"
    ]
    
    for query in test_queries:
        print(f"\n🔍 Searching for: '{query}'")
        print("-" * 40)
        result = tool.run(query)
        print(result)
        print("=" * 50)


# Create a CrewAI-compatible tool wrapper
class CategorySearchInput(BaseModel):
    """Input schema for category search"""
    query: str = Field(description="The search query describing the product to categorize")


class CategorySearchTool(BaseTool):
    """CrewAI-compatible wrapper for CategoryHierarchyTool"""
    
    name: str = "category_search"
    description: str = (
        "Search the category hierarchy database to find the best matching categories "
        "for a product based on keywords from its name, description, or characteristics. "
        "Returns structured category paths with all 7 levels."
    )
    args_schema: Type[BaseModel] = CategorySearchInput
    hierarchy_tool: CategoryHierarchyTool = None
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Initialize the actual tool after parent init
        if self.hierarchy_tool is None:
            self.hierarchy_tool = CategoryHierarchyTool()
    
    def _run(self, query: str) -> str:
        """Execute the category search"""
        return self.hierarchy_tool.run(query)


if __name__ == "__main__":
    test_category_tool()
