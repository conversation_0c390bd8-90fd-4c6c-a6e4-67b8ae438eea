# Performance Optimization Guide

## 📈 Performance Overview

The Category Mapping System has been extensively optimized for high-throughput processing with comprehensive monitoring and profiling capabilities.

## 🎯 Current Performance Metrics

### Baseline Performance (After Optimization)
- **End-to-End Throughput:** 1.24 products/second
- **Parallel Speedup:** 1.26x faster (20.8% time savings)
- **FAISS Search:** 764ms average (optimized from 1000ms+)
- **Agent Execution:** Mapper ~10.4ms, Validation ~5.5ms
- **Memory Usage:** 40.1MB for 3,424 embeddings

### Performance Benchmarks by Component

| Component | Sequential | Parallel | Improvement |
|-----------|------------|----------|-------------|
| FAISS Search | 1000ms+ | 764ms avg | 23%+ faster |
| Agent Processing | 15.9ms | 60-70ms batch | 26x throughput |
| Database Operations | Variable | Pooled | Connection reuse |
| Overall Workflow | ~20s/20 products | ~16s/20 products | 20.8% faster |

## ⚡ Optimization Strategies

### 1. FAISS Index Optimization

#### Index Type Performance Comparison
```python
# Benchmark results for 3,424 embeddings
Index Types:
├── Flat:     58ms benchmark time (baseline)
├── IVFFlat:  13ms benchmark time (4.5x faster) ⚡
├── IVFPQ:    31ms benchmark time (1.9x faster)
└── HNSW:     7ms benchmark time (8.3x faster) 🔥
```

#### Recommended Configurations
```python
# For Speed (Recommended)
config = IndexOptimizationConfig(
    index_type="HNSW",
    nlist=32,           # M parameter for HNSW
    use_gpu=False       # CPU optimized
)

# For Memory Efficiency
config = IndexOptimizationConfig(
    index_type="IVFPQ",
    nlist=100,
    nprobe=10,
    m=8,                # Subquantizers
    nbits=8             # Bits per subquantizer
)

# For Accuracy
config = IndexOptimizationConfig(
    index_type="IVFFlat",
    nlist=100,
    nprobe=20           # Higher nprobe for accuracy
)
```

#### Memory Optimization
```python
# Current usage: 40.1MB for 3,424 embeddings
# IVFPQ can reduce memory by 50-80%

# Enable memory mapping for large datasets
config.memory_map = True

# Use product quantization
config.m = 8        # 8 subquantizers
config.nbits = 8    # 8 bits per subquantizer
# Result: ~8-10MB memory usage (75% reduction)
```

### 2. Parallel Processing Implementation

#### FAISS Parallel Search
```python
faiss_processor = ParallelFAISSProcessor(
    max_workers=4,      # CPU cores for FAISS
    batch_size=20       # Queries per batch
)

# Results: 1.31 queries/second throughput
results = faiss_processor.search_batch(queries, k=5)
```

#### Agent Parallel Processing
```python
agent_processor = ParallelAgentProcessor(
    max_workers=2,      # Conservative for LLM calls
    batch_size=10       # Products per batch
)

# Results: 26.36 products/second potential
batch_result = agent_processor.process_batch(products)
```

#### Database Async Operations
```python
db_processor = AsyncDatabaseProcessor(
    connection_pool_size=5  # Pooled connections
)

# Batch database updates
db_results = db_processor.batch_update_mappings(mappings)
```

### 3. Workflow Orchestration
```python
# Complete parallel workflow
orchestrator = ParallelWorkflowOrchestrator(
    faiss_workers=4,
    agent_workers=2,
    db_pool_size=5
)

results = orchestrator.process_products_parallel(
    products,
    enable_faiss_parallel=True,
    enable_agent_parallel=True
)

# Results:
# - 1.24 products/second end-to-end
# - 1.26x speedup factor
# - 20.8% time savings
```

## 📊 Performance Monitoring

### Profiling Tools

#### Operation Profiling
```python
from performance_profiler import get_profiler

profiler = get_profiler()

# Profile any operation
with profiler.profile_operation("my_operation"):
    # Your code here
    result = expensive_operation()

# Get metrics
metrics = profiler.metrics[-1]  # Latest operation
print(f"Duration: {metrics.duration_ms}ms")
print(f"Memory: {metrics.memory_mb}MB")
print(f"CPU: {metrics.cpu_percent}%")
```

#### Database Query Profiling
```python
@profiler.profile_database_query("SELECT", "SELECT * FROM categories WHERE...")
def my_database_query():
    # Database operation
    return cursor.fetchall()

# Automatic metrics collection:
# - Execution time
# - Rows affected/returned
# - Connection time
# - Success/failure tracking
```

#### Agent Execution Profiling
```python
@profiler.profile_agent_execution("mapper_agent", "product_mapping")
def execute_mapper_agent(product):
    return mapper_agent.execute_task(mapping_task)

# Tracks:
# - LLM calls and timing
# - Tool calls and timing
# - Token usage
# - Success/failure rates
```

### Performance Reports

#### Generate Comprehensive Report
```python
# Generate detailed performance report
report_file = profiler.generate_performance_report()

# Report includes:
# - Operation summaries
# - Database query analysis
# - Agent execution metrics
# - System resource usage
# - Performance trends
```

#### Benchmark Workflows
```python
# Benchmark complete workflow
benchmark_results = profiler.benchmark_workflow(
    products=test_products,
    iterations=3
)

print(f"Avg throughput: {benchmark_results['avg_throughput_per_second']:.2f}")
print(f"Avg time per product: {benchmark_results['avg_time_per_product_ms']:.2f}ms")
```

## 🔧 Performance Tuning

### FAISS Optimization
```python
# Run FAISS optimization analysis
from faiss_optimizer import FAISSOptimizer

optimizer = FAISSOptimizer()
optimization_results = optimizer.optimize_index()

# Get recommendations
for rec in optimization_results['recommendations']:
    print(f"💡 {rec['recommendation']}")
    print(f"   Impact: {rec['impact']}")
```

### Parallel Processing Tuning
```python
# Tune worker counts based on system resources
import multiprocessing as mp

# FAISS workers (I/O bound)
faiss_workers = mp.cpu_count()  # Can use all cores

# Agent workers (API bound)
agent_workers = min(mp.cpu_count(), 4)  # Conservative for LLM calls

# Database workers
db_pool_size = min(mp.cpu_count(), 10)  # Based on DB capacity
```

### Memory Optimization
```python
# Monitor memory usage
memory_analysis = analyzer.analyze_memory_usage()

print(f"Process memory: {memory_analysis['process_memory_mb']:.1f}MB")
print(f"Memory freed by GC: {memory_analysis['memory_freed_by_gc_mb']:.1f}MB")

# Optimize memory usage
if memory_analysis['process_memory_mb'] > 500:
    # Consider IVFPQ quantization
    # Enable memory mapping
    # Implement embedding sharding
```

## 📊 Performance Analysis

### Running Performance Analysis
```python
# Comprehensive analysis
python analyze_performance.py

# Output includes:
# - FAISS search benchmarks
# - Database query performance
# - Agent execution metrics
# - Memory usage analysis
# - Optimization recommendations
```

### Key Metrics to Monitor
1. **Throughput:** Products processed per second
2. **Latency:** Time per product mapping
3. **Success Rate:** Percentage of successful mappings
4. **Resource Usage:** CPU, memory, disk I/O
5. **Error Rate:** Failed operations per batch

### Performance Thresholds
```python
# Performance targets
PERFORMANCE_TARGETS = {
    'throughput_per_second': 2.0,      # Target: 2+ products/second
    'faiss_search_ms': 500,            # Target: <500ms per search
    'agent_execution_ms': 50,          # Target: <50ms per agent
    'memory_usage_mb': 200,            # Target: <200MB process memory
    'success_rate_percent': 95,        # Target: >95% success rate
    'database_query_ms': 100           # Target: <100ms per query
}
```

## 🚨 Performance Troubleshooting

### Common Performance Issues

#### Slow FAISS Searches
```python
# Symptoms: Search times >1000ms
# Solutions:
1. Switch to HNSW index (7ms vs 58ms)
2. Reduce embedding dimension
3. Use product quantization (IVFPQ)
4. Enable parallel search processing

# Implementation:
optimizer = FAISSOptimizer()
results = optimizer.benchmark_index_types(test_queries)
# Choose fastest index type
```

#### Low Throughput
```python
# Symptoms: <1 product/second
# Solutions:
1. Enable parallel processing
2. Increase worker counts
3. Optimize database queries
4. Implement caching

# Implementation:
orchestrator = ParallelWorkflowOrchestrator(
    faiss_workers=8,    # Increase workers
    agent_workers=4,    # Increase workers
    db_pool_size=10     # Larger pool
)
```

#### High Memory Usage
```python
# Symptoms: >500MB memory usage
# Solutions:
1. Use IVFPQ quantization (50-80% reduction)
2. Enable memory mapping
3. Implement embedding sharding
4. Optimize garbage collection

# Implementation:
config = IndexOptimizationConfig(
    index_type="IVFPQ",
    m=8, nbits=8,       # Quantization
    memory_map=True     # Memory mapping
)
```

#### Database Bottlenecks
```python
# Symptoms: High database query times
# Solutions:
1. Enable connection pooling
2. Use async database operations
3. Optimize SQL queries
4. Add database indexes

# Implementation:
db_processor = AsyncDatabaseProcessor(
    connection_pool_size=10
)
```

## 🔄 Continuous Performance Monitoring

### Automated Monitoring Setup
```python
# Schedule regular performance analysis
import schedule

def run_performance_check():
    analyzer = PerformanceAnalyzer()
    results = analyzer.run_comprehensive_analysis()
    
    # Check for performance regressions
    if results['workflow_benchmark']['avg_throughput_per_second'] < 1.0:
        logger.log_event(
            LogLevel.WARNING,
            "Performance regression detected",
            category=ErrorCategory.CONFIGURATION,
            current_throughput=results['workflow_benchmark']['avg_throughput_per_second']
        )

# Run every hour
schedule.every().hour.do(run_performance_check)
```

### Performance Alerts
```python
# Set up performance thresholds
def check_performance_thresholds(metrics):
    alerts = []
    
    if metrics.get('avg_search_time_ms', 0) > 1000:
        alerts.append("FAISS search time exceeds 1000ms")
    
    if metrics.get('throughput_per_second', 0) < 1.0:
        alerts.append("Throughput below 1 product/second")
    
    if metrics.get('memory_usage_mb', 0) > 500:
        alerts.append("Memory usage exceeds 500MB")
    
    return alerts
```

## 📈 Scaling Recommendations

### Horizontal Scaling
1. **Multi-Instance Deployment:** Run multiple mapping instances
2. **Load Balancing:** Distribute products across instances
3. **Database Sharding:** Partition data across databases
4. **Microservices:** Split components into separate services

### Vertical Scaling
1. **CPU Optimization:** Increase worker counts
2. **Memory Optimization:** Use IVFPQ quantization
3. **Storage Optimization:** SSD for FAISS indexes
4. **Network Optimization:** Reduce API call latency

### Production Deployment
```python
# Production configuration
PRODUCTION_CONFIG = {
    'faiss_workers': 8,
    'agent_workers': 4,
    'db_pool_size': 20,
    'batch_size': 100,
    'index_type': 'HNSW',
    'enable_caching': True,
    'monitoring_enabled': True
}
```

---

For implementation details, see [API Reference](api_reference.md) and [Testing Guide](testing.md).
