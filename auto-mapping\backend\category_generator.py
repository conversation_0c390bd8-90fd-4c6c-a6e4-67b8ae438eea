import os
from openai import OpenAI
import pandas as pd
from dotenv import load_dotenv
from scipy.spatial.distance import cosine
import warnings
import tiktoken
import re 
import inflect 
import logging
from backend.database import database
from backend.preprocess import preprocess

load_dotenv()
warnings.filterwarnings("ignore")

class category_generator:
    """
    A class to get the product category of various levels using the OpenAI API.

    This class utilizes OpenAI's GPT models to predict the product category based on the product title and description.
    The class also calculates costs associated with the API usage and returns the category assigned for each level as a dictionary.
    The analysis along with the categories is update into a MySQL table.

    Attributes:
        client (OpenAI): The OpenAI client configured with an API key.
        model (str): The identifier for the OpenAI model to use.
        token_model (embedding): The model for getting the embeddings of text.
        product_id (int): The product id of the product.
        product_name (str): The name of the product.
        product_description (str): The description of the product.
        cleaned_description (str): The description with removed unwanted text.
        output_df (dataframe): The data frame containing all the analysis columns and final category.
    """
    def __init__(self):
        self.client = OpenAI(api_key = os.environ['OPENAI_API_KEY'])
        # self.model = "gpt-3.5-turbo"
        self.model = "gpt-4o-mini"
        self.token_model = 'text-embedding-3-large'
        # self.product_id = product_id
        # self.product_name = product_name
        # self.product_description = product_description
        self.preprocess = preprocess()
        self.p = inflect.engine()
        self.levels = ['Level 1', "Level 2", "Level 3", "Level 4","Level 5","Level 6","Level 7"]
        # self.cleaned_description = self.preprocess.remove_ingredients(self.preprocess.extract_text(product_description))
        # self.product_embed, self.product_embed_cost = self.get_embedding(self.product_name + " "+ self.cleaned_description)
        # self.product_embed = None
        # self.product_embed_cost = 0
        # self.output_df = pd.DataFrame(columns= ["product_id","product_name",'product_description','product_embed_cost',"category_path_hardlogic","keywords_nouns","keywords_kf","main_item","category_path_keyword","main_item_cost","embed_cost_nouns",'cost_mainitem_category',"options1","llm_output1","cost1","options2","llm_output2","cost2","options3","llm_output3","cost3",'Level 1', "Level 2", "Level 3"])
        # try:
        #     self.database = database()
        # except Exception as e:
        #     raise Exception(f"{str(e)}")
        
        logging.info(f"Initiating the category_generator class.")



    def get_cost_from_response(self,response,model):
        """
        Calculates the cost of using the OpenAI API based on token usage.

        :param response: Response object from OpenAI API.
        :param model: Model used for generating response
        :return: Calculated cost based on input and output token usage.
        """
        if model == 'gpt-4o':
            price_per_k_input_token = 0.005  # cost in $ for 1000 input tokens
            price_per_k_output_token = 0.015 # cost in $ for 1000 output tokens
            
        if model == 'gpt-3.5-turbo':
            price_per_k_input_token = 0.0005  # cost in $ for 1000 input tokens
            price_per_k_output_token = 0.0015 # cost in $ for 1000 output tokens

        if model == "gpt-4o-mini":
            price_per_k_input_token = 0.000150
            price_per_k_output_token = 0.000600

        input_tokens = response.usage.prompt_tokens
        output_tokens = response.usage.completion_tokens

        cost = round(((price_per_k_input_token / 1000) * input_tokens) + (
                    (price_per_k_output_token / 1000) * output_tokens), 5)

        return cost
    
    
    def embed_price_from_text(self,text):
        """Calculates the cost for embeddings based on the number of tokens in a text string.
        
        :param text: String from which the number of tokens and cost is to be calculated.
        :return: Cost of embedding the text.
        """
        encoding = tiktoken.get_encoding("cl100k_base")
        num_tokens = len(encoding.encode(text))
        price_per_k_tokens = 0.00013
        price = num_tokens * price_per_k_tokens / 1000
        return price
    
    
    def get_embedding(self,text):
        """Converts the text string into embeddings.

        :param text: The text that needs to be converted to embedding.
        :return: Embedding of the text along with the cost for embedding.
        """
        text = text.replace("\n", " ")
        price = self.embed_price_from_text(text)
        return self.client.embeddings.create(input = [text], model=self.token_model).data[0].embedding, price
    
    
    def get_mainitem_from_llm(self,matched_items):
        """Gets the main item from the list of matched items based on the product name and description.

        :param matched_items: The list of matched_words that the LLM must choose from.
        :return: Main item from the list of matched items.
        """
        logging.info(f"Generating main item of the product from LLM.")
        model = self.model

        prompt = f"""
        Do the task as accurately as possible.
        Based on the product name "{self.product_name}" with the description "{self.cleaned_description}", use your intelligence to tell what word exactly can be used to identify this product. If there is no direct word that tells what product is this, then which word directly points to product category that this product falls in exactly by picking only one of the options from below. Correctly map what this product is.
        
        Conider the below rules as well over product name and description and ensure these are followed:  
        - Make sure if it is biscuit it should only be mapped with that option. 
        - Make sure that if the product is a baby toys or baby games or baby food or baby craft or art then it should only be mapped to that option, if not map it directly to "toys".Do not give "discover" 
        - Make sure that if the product is a medicine or supplement or any related medicinal product then it should only be mapped to that category option. There are no gels in this.
        - Make sure that if the product is a dental related product then it should only be mapped to that category option from the list. There is no "paste" or "gel" in the list but map these to "toothpaste" only.
        - Make sure that if the product is a laundry and cloth related product then it should only be mapped with that category.
        - Make sure that if it is a pet needs or pet food or pet toys or pet care product like cat or dog or kitten, then it should only be mapped with that option.
        - Make sure that if it is a stationery item such as file holders or pens or pencils or any related product, then it should only be mapped with that option.
        - Make sure that if it is a beer, alcohol, wine, mixer, spirit or similar related items, then it should only be mapped to option 'beer'.
        - Make sure that if it has stop-smoking aid for smokers then it should only be mapped to that option.
        - Make sure that if it is a gift or gift wrap related item then it should mapped with "gift" only.
        - Make sure that baby oils or baby creams and moisturisers are mapped to the option "baby" only.
        - Make sure that if it is a teething product then it should only be mapped to that option only.
        - Make sure that if it is a sleep or calming aid then it shoudl be mapped to that option only.
        - Make sure that if it is a pasta then it should only be mapped to that option.
        - "Make sure that if the product name contains "seabrook" in it then give the answer as "seabrook" only, do not give other options."
        - Make sure that if it is a medicine for treating allergy or hayfever or diarrhoea or gastrointestinal then it must only be mapped to that option of illness only. Do not give "medicine"
        - Make sure that if it is "eau de parfum" or a body spray or perfume then it must be mapped to that option only. Do not give "scent".
        - Make sure if it is a vapour rub then it should only be mapped to that option.
        - Make sure if it is a sauce then it should be mapped to that options only.
        - Make sure if it is a skin related product then it should be only mapped to that option.
        - Make sure if these are seeds then it should only be mapped to that option.
        - Make sure that if it has "washing up liquid" or a dishwasher in options then it should only be mapped to that option.
        - Make sure that if it is a cake baking tray or tin or "pastry brush" or "baugette tray" then it should only be mapped to that option only.
        - Make sure that if it is a flower remedy then it should only be mapped to that option only.
        - Make sure if it is a sweetener or sweetener tablet then directly give the answer as "sugar" only.
        - Make sure if it is a honey or honey spread then it should be mapped to "honey" only.
        - Make sure if it is an after birth care product or maternity product then directly give the answer as "maternity" only.
        - Make sure if it is a squash then it should be only mapped to that option.
        - Make sure that the words such as 'packs' or 'pack' or 'set' or 'sets' are ignored.
            
        Options : {', '.join(f'"{option}"' for option in matched_items)}

        GIVE ACCURATE ANSWERS ONLY FROM THE LIST GIVEN. JUST PICK AND GIVE ONE ANSWER FROM THE LIST. Just give only the word from the option and nothing else. DO NOT GIVE ADDITIONAL TEXT.
        """
        response = self.client.chat.completions.create(
            messages=[
                {
                    'role':'system',
                    'content': 'You are a ecommerce product expert that knows how to identify products.'

                },
                {
                    'role':'user',
                    'content':prompt,
                }
            ],
            model = model ,
            temperature=0,
        )
        cost = self.get_cost_from_response(response, model)
        logging.info(f"Main item generated.")
        return response.choices[0].message.content.strip("'\"*><"), cost
    
    
    def generate_category_word_list(self):
        """Generate a list of words that are from category.
        """
        category_word_list = set()
        categories = self.database.get_categories()
        for row in categories:
            for category in row:  # Use row.values() to get category names
                if category: 
                    words = [self.p.singular_noun(word.lower().strip()) or word.lower().strip() 
                             for word in re.findall(r'\b\w+\b', category)]
                    category_word_list.update(words)

        self.category_word_list = category_word_list

    
    def matching_title_categories(self):
        """
        Match the words from the prodcut name with the category words.

        :return: List of words from the product name that are matched with the category words.
        """
        title_words = [self.p.singular_noun((word.lower())) or word.lower() for word in self.preprocess.extract_nouns(self.preprocess.remove_weight(self.product_name))]
        title_words += [word.lower() for word in self.preprocess.extract_nouns(self.preprocess.remove_weight(self.product_name))]
        title_words = list(set(title_words))
        matched_words = set(title_words).intersection(set(self.category_word_list))   
        return list(matched_words)
    
    
    def matching_description_categories(self):
        """
        Match the words from the product description with the category words.

        :return: List of words from the description that are matched with the category words
        """
        description_words = [self.p.singular_noun(word.lower().strip()) or word.lower().strip() for word in self.preprocess.extract_nouns(self.cleaned_description)]
        matching_words = set(description_words).intersection(set(self.category_word_list))
        return list(matching_words)
    
    
    def find_matching(self):
        """Find the matching category for the product name and description with the category words and extract the top 6 words based on the embedding match with the product name & description.

        :return: The top 6 words from the match and the cost for creating the embeddings of the words.
        """
        logging.info("Finding matching noun words from title and description with categories and filtering the top 6 nouns.")
        title_categories = self.matching_title_categories()
        description_categories = self.matching_description_categories()
        union_title_description = list(set(title_categories).union(set(description_categories)))

        if self.product_embed is None:
            self.product_embed, self.product_embed_cost = self.get_embedding(self.product_name + " "+ self.cleaned_description)

        similarity_list = {}
        embed_cost = 0
        for word in union_title_description:
            word_embed, price = self.get_embedding(word)
            embed_cost += price
            similarity = 1 - cosine(word_embed,self.product_embed)
            similarity_list[word] = similarity
        
        sorted_categories = sorted(similarity_list.items(), key=lambda x: x[1], reverse=True)
        filtered_categories = [category for category, similarity in sorted_categories[:6]]
        
        return filtered_categories, embed_cost
    

    def get_explanation(self,product_category,keyword_list):
        """Get the explanation of the product category of the next level.

        :param product_category: The category of the product till current level.
        :param keyword_list: The list of next level categories.
        :return: Dictionary with the next level category as key and the explanation as value.
        """
        product_category = product_category.rstrip(' > ')
        keyword_to_explanation = {}
        if product_category == '':
            category_paths = keyword_list
        else:
            category_paths = [' > '.join([product_category]+[keyword]) for keyword in keyword_list]

        for category_path, item in zip(category_paths, keyword_list):
            explanation = self.database.get_explanation_by_category(category_path)
            if explanation:
                explanation = explanation[0]
            else:
                explanation = 'No explanation available for this category'
            keyword_to_explanation[item] = explanation

        return keyword_to_explanation
    
    

    def get_category_llm(self,product_category,options):
        """Get the category of the next level using the LLM.

        :param product_category: Category of the product till the current level.
        :param options: List of options for the next level category.
        :return: The category of the next level and the cost for generating the answer.
        """
        model = self.model
        explanations = self.get_explanation(product_category, options)
        explanations_str = ',\n\n'.join(f'""{key}"" : {value}' for key, value in explanations.items())

        prompt =  f"""Do the task as accurately as possible.
    Here is a product : "{self.product_name}" with the description : "{self.cleaned_description}".

    This product is available on an ecommerce store under category "{product_category}". Now analyse the product and tell what should be the next level of category using your intelligence from the list of options that are given for an ecommerce store. Sometimes the description may be misleading use your intelligence to correctly choose the category that best suits the product and also take the help of the explanations. The options are in json format next category and then its explanation. Make sure to properly use the explanation to choose the correct category that it belongs to.
    Select/give just the answer only by the name of the category from the options given in the list below.
    
    DO NOT GIVE ANYTHING OTHER THAN THE ONES FROM OPTIONS. JUST GIVE THE CATEGORY FOR THIS LEVEL AS THE ANSWER ONLY FROM THE OPTIONS. THE ANSWERS SHOULD ONLY BE FROM THE OPTIONS PROVIDED AND NOTHING ELSE. DO NOT GIVE ADDITIONAL TEXT. MAKE SURE YOU PICK THE OPTION FOR THAT LEVEL ONLY.
    "Do not try to pick that is not from the options. Do not hallucinate to generate outputs that don't exactly match with the options. Do not pick the option from the explanation, just use the explanation to select from the given options.
    Make sure not to hallucinate in this task."
    
    Options: 
    {explanations_str}
    """
        response = self.client.chat.completions.create(
            messages=[
                {
                    'role':'system',
                    'content': 'You are a ecommerce product expert that knows how to identify categories of product.'

                },
                {
                    'role':'user',
                    'content':prompt,
                }
            ],
            model = model,
            temperature=0,
        )
        cost = self.get_cost_from_response(response,model)
        return response.choices[0].message.content.strip("'\"*><"), cost
    

    def get_category_mainitem_llm(self,options):
        """Get the category from a list of category that have a matching with the main item word using the LLM.
        :param options: The list of categories from which the LLM must choose.
        :return: The category the product and the cost for generating the answer.
        """
        explanations = self.get_explanation('',options)
        explanations_str = ',\n'.join(f'""{key}"" : {value}' for key, value in explanations.items())

        model = self.model
        
        prompt = f"""Do the task as accurately as possible.
        Here is a product : "{self.product_name}" with the description : "{self.cleaned_description}".

        Now analyse the product and tell me what should be the category of this product for an ecommerce store using your intelligence from \
        options that are given. Sometimes the description may be misleading use your intelligence to correctly choose the category that best \
        suits the product. Make sure that the correct option is picked from the options. The options are of the format - category path as the \
        key present in quotation marks and then followed by its explanation. Make sure you use the explanation also to correctly give what \
        category path it belongs to.
        Select/give just the answer only by the category path from the options given in the list below. Go through the options properly and \
        select the best category path.
        Make sure that if the product is baby oil or baby lotion or baby moisturisers or baby gromming products, then it should only be mapped \
        to "Baby, Parent & Kids".
        Make sure that if the product is a normal paper roll, then it should be mapped to option that is related to wrapping.
        Make sure that books are correctly mapped to the option.
        "MAKE SURE YOU ONLY RETURN THE EXACT FULL PATH IN BETWEEN <""> ONLY FROM THE OPTIONS THAT IS THE BEST CATEGORY. DO NOT ADD ANY \
        ADDITIONAL TEXT. MAKE SURE YOU DON'T GIVE THE PARTIAL KEY OR PARTIAL PATH AS ANSWER. DO NOT GIVE ANYTHING OTHER THAN OPTION." Make sure \
        all levels of categories exactly matching with the options without missing any levels between two levels. Do not hallucinate to generate \
        outputs that don't exactly match with the options. Make sure you do not hallucinate in this task.
        
        Options: 
        {explanations_str}

    """
        response = self.client.chat.completions.create(
            messages=[
                {
                    'role':'system',
                    'content': 'You are a ecommerce product expert that knows how to identify category of product.'

                },
                {
                    'role':'user',
                    'content':prompt,
                }
            ],
            model = model,
            temperature=0,
        )
        cost = self.get_cost_from_response(response,model)
        return response.choices[0].message.content.strip("'\"*><") , cost
    

    def get_category_mainitem(self,main_item):
        """Get the category of the product by the main item search in the list of categories.

        :param main_item (str): The main item to get the relevant category of product.
        :return: The category path of the product.
        """
        logging.info("Extracting the possible categories that match with the main item.")
        # levels = self.database.get_columns_categories()
        levels = self.levels
        main_item = self.p.singular_noun(main_item) or main_item    
        matched_categories = set()

        def should_check(row, current_level):
            if current_level > 0:
                for j in range(current_level):
                    parent_category_path = ' > '.join([str(row[levels[k]]) for k in range(j + 1) if row[levels[k]] is not None])
                    if parent_category_path in matched_categories:
                        return False
            return True

        matched_rows = []
        for i in range(len(levels)):
            rows_to_check = self.database.get_rows_categories(levels[i])

            for row in rows_to_check:
                row_dict = dict(zip(levels, row))
                if should_check(row_dict, i):
                    for word in re.findall(r'\b\w+\b', str(row[i])):
                        singular_word = self.p.singular_noun(word.strip()) or word.strip()
                        if main_item.lower() == singular_word.lower():
                            row_dict['match_level'] = i
                            matched_categories.add(' > '.join([str(row_dict[levels[j]]) for j in range(i + 1) if row_dict[levels[j]] is not None]))
                            matched_rows.append(row_dict)
                            break
        
        if not matched_rows:
            logging.info(f"No Category found with main item matching.")
            return '', -1, 0
        
        results = set()
        for row in matched_rows:
            i_level = row['match_level']
            path = []
            for j in range(i_level + 1):
                if row[levels[j]] is not None:
                    path.append(row[levels[j]])
            results.add((i_level, ' > '.join(path)))
        
        results = list(results)
        options = [result[1].strip() for result in results]

        if len(results)>1:
            category_path, cost = self.get_category_mainitem_llm(options)
            if category_path in options:
                i_value = results[options.index(category_path)][0]
                logging.info(f"Category found till Level {i_value+1} by main item match.")
            else:
                category_path = ""
                i_value = -1
                logging.info(f"No Category found with main item matching.")
        else:
            category_path = results[0][1]
            cost=0
            i_value = results[0][0]
            logging.info(f"Category found till Level {i_value+1} by main item match.")
        return category_path, i_value, cost
    

    def get_category_next(self,category_path,val):
        """ Get all the next level categories till final level.

        :param category_path: Category of the product till the currect level.
        :param val: The level till which the category is available.
        :return: The category path of the product till the last level.
        """
        # levels = self.database.get_columns_categories()
        levels = self.levels
        category_split = [word.strip('>').strip() for word in category_path.split(' > ')]
        is_valid = True

        if val+1== len(levels):
            return category_path
        
        if val!= -1:
            category_list = self.database.get_category_list(levels[val],levels[val+1],category_path)
            cat_concat = [category_path +' > '+ item for item in category_list]
            if len(category_list)==0:
                return category_path
            
        # self.output_df[f'is_hallucination'] = self.output_df.apply(lambda row: 0 if row['product_name'] == self.product_name else row[f'is_hallucination'], axis=1)

        for iter in range(val+1,len(levels)):
            if self.product_embed is None:
                self.product_embed, self.product_embed_cost = self.get_embedding(self.product_name + " "+ self.cleaned_description)

            explanation_embeds = {}
            for item in cat_concat:
                embeds = self.database.get_explanation_embed(item)
                if embeds:
                    explanation_embeds[item.split(' > ')[-1]] = embeds
                    
            
            sim = {}
            for cat, embed in explanation_embeds.items():
                similarity = 1 - cosine(self.product_embed, embed)
                sim[cat] = similarity

            sorted_categories = sorted(sim.items(), key=lambda x: x[1], reverse=True)
            options = [category_item for category_item, similarity in sorted_categories[:5]]            

            self.output_df[f'options{iter+1}'] = self.output_df.apply(lambda row: str(options) if row['product_name'] == self.product_name else row[f'options{iter+1}'], axis=1)

            get_category_response, cost = self.get_category_llm(category_path, options)
            out = get_category_response

            self.output_df[f'llm_output{iter+1}'] = self.output_df.apply(lambda row: out if row['product_name'] == self.product_name else row[f'llm_output{iter+1}'], axis=1)

            self.output_df[f'cost{iter+1}'] = self.output_df.apply(lambda row: cost if row['product_name'] == self.product_name else row[f'cost{iter+1}'], axis=1)
            
            if out in options:
                category_path = category_path.rstrip(' > ')
                category_path += f" > {out}"

                logging.info(f'Getting category for Level {iter+1}.')

                if iter < len(levels) - 1:
                    next_level_column = levels[iter+1]
                    category_list = self.database.get_category_list(levels[iter],next_level_column,category_path)
                    cat_concat = [category_path + " > " + item for item in category_list]

                    if len(category_list) == 0:
                        break
            else:
                logging.warning(f'GPT output "{out}" is not from the provided options. Skipping all updates for this product.')
                is_valid = False
                break

        if not is_valid:
            self.output_df[f'is_hallucination'] = self.output_df.apply(lambda row: 1 if row['product_name'] == self.product_name else row[f'is_hallucination'], axis=1)
            # return None
            self.output_df['product_embed_cost'] = [self.product_embed_cost]
            self.database.update_table_from_df(self.product_id,self.output_df)
            raise ValueError("LLM output hallucinated.")

        return category_path.strip(' > ')
    

    def get_product_category_sim(self):
        """Get the category of each level with the similarity scores between the product and the category explanation.
        :returns: The category path of the product till the last level.
        """
        # levels = self.database.get_columns_categories()
        levels = self.levels
        category = self.database.get_category_bylevel(levels[0])
        is_valid = True

        if self.product_embed is None:
            self.product_embed, self.product_embed_cost = self.get_embedding(self.product_name + " "+ self.cleaned_description)

        prod=''
        # self.output_df[f'is_hallucination'] = self.output_df.apply(lambda row: 0 if row['product_name'] == self.product_name else row[f'is_hallucination'], axis=1)
        for k in range(len(levels)): 
            explanation_embeds = {}
            for item in category:
                embeds = self.database.get_explanation_embed(item)
                # if embeds and embeds[0]:
                #     explanation_embeds[item] = eval(embeds[0])
                if embeds:
                    explanation_embeds[item.split(' > ')[-1]] = embeds

            sim = {}
            for cat, embed in explanation_embeds.items():
                similarity = 1 - cosine(self.product_embed, embed)
                sim[cat] = similarity
                
            sorted_categories = sorted(sim.items(), key=lambda x: x[1], reverse=True)
            filtered_categories = [category for category, similarity in sorted_categories[:5]]
            options = [option.split('>')[-1].strip() for option in filtered_categories]
            
            self.output_df[f'options{k+1}'] = self.output_df.apply(lambda row: str(options) if row['product_name'] == self.product_name else row[f'options{k+1}'], axis=1)

            get_category_response, cost = self.get_category_llm(prod, options)
            out = get_category_response

            
            self.output_df[f'llm_output{k+1}'] = self.output_df.apply(lambda row: out if row['product_name'] == self.product_name else row[f'llm_output{k+1}'], axis=1)

            self.output_df[f'cost{k+1}'] = self.output_df.apply(lambda row: cost if row['product_name'] == self.product_name else row[f'cost{k+1}'], axis=1)
            
        #     prod += f"{out} > "

        #     logging.info(f'Getting category for Level {k+1}.')

        #     if k < len(levels) - 1:
        #         next_level_column = levels[k + 1]
        #         category_list = self.database.get_category_list(levels[k],next_level_column,out)
        #         category = [prod + item for item in category_list]
        #         if len(category_list) == 0:
        #             break
        # return prod.rstrip(' > ')
            if out in options:
                prod += f"{out} > "

                logging.info(f'Getting category for Level {k+1}.')

                if k < len(levels) - 1:
                    next_level_column = levels[k + 1]
                    category_list = self.database.get_category_list(levels[k], next_level_column, prod.rstrip(" > "))
                    category = [prod + item for item in category_list]

                    if len(category_list) == 0:
                        break
            else:
                logging.warning(f'GPT output "{out}" is not from the provided options. Halting category assignment for this product.')
                is_valid = False
                break

        if not is_valid:
            self.output_df[f'is_hallucination'] = self.output_df.apply(lambda row: 1 if row['product_name'] == self.product_name else row[f'is_hallucination'], axis=1)
            # return None
            self.output_df['product_embed_cost'] = [self.product_embed_cost]
            self.database.update_table_from_df(self.product_id,self.output_df)
            raise ValueError("LLM output hallucinated.")


        return prod.rstrip(' > ')
        

    def get_category_kf(self,main_word):
        """Get the category of the product with the match of the main_word with the Keyword in KF.

        :param main_word: The main word of the product.
        :return: category: The category path of the product till the level that is available.
        """
        logging.info('Getting category from KF with the matched word of the product.')
        main_word = main_word.lower().strip()
        direct_match =  self.database.fetch_category_kf(main_word)

        if direct_match:
            category_kf = []
            
            for value in direct_match:
                if value is not None:
                    category_kf.append(value)
            
            return ' > '.join(category_kf), len(category_kf) - 1
        else:
            return '', -1
        
    def get_keyword_kf(self):
        """Get the keywords of the product with the match of the words of product name and description with the Keyword in KF.

        :return: keyword: The list of keyword that match with the product.
        """
        matched_words = []
        combined_text = self.preprocess.remove_weight(self.product_name) + " " + self.cleaned_description
        combined_text = combined_text.lower().strip()
        
        singularized_text = ' '.join([self.p.singular_noun(word) or word for word in combined_text.split()])
        keywords = self.database.fetch_kf_keywords()
        for keyword in keywords:
            pattern = r'\b' + re.escape(keyword) + r'\b'
            if re.search(pattern, combined_text) or re.search(pattern, singularized_text):
                matched_words.append(keyword)
        
        return matched_words
    

    def get_category_nouns(self):
        """Get the category of the product with the match of the nouns words of product name and description.

        :return: Full category path of the product
        """
        logging.info('Getting category using noun words and KF words of the product name and description.')
        self.generate_category_word_list()
        keywords_noun, embed_cost = self.find_matching()
        keywords_kf = self.get_keyword_kf()

        matched_keywords = list(set(keywords_noun+keywords_kf))


        self.output_df[f'keywords_nouns'] = self.output_df.apply(lambda row: str(keywords_noun) if row['product_name'] == self.product_name else row[f'keywords_nouns'], axis=1)

        
        self.output_df[f'soft_logic_words'] = self.output_df.apply(lambda row: str(keywords_kf) if row['product_name'] == self.product_name else row[f'soft_logic_words'], axis=1)

        self.output_df[f'embed_cost_nouns'] = self.output_df.apply(lambda row: embed_cost if row['product_name'] == self.product_name else row[f'embed_cost_nouns'], axis=1)
        
        if len(matched_keywords)>0:
            main_item, main_item_cost = self.get_mainitem_from_llm(matched_keywords)
            self.output_df[f'main_item'] = self.output_df.apply(lambda row: main_item if row['product_name'] == self.product_name else row[f'main_item'], axis=1)

            self.output_df[f'main_item_cost'] = self.output_df.apply(lambda row: main_item_cost if row['product_name'] == self.product_name else row[f'main_item_cost'], axis=1)
            
            if main_item.lower() in keywords_kf:
                category_path, level = self.get_category_kf(main_item)
                cost_cat = 0
                logging.info(f"Category found till Level {level+1} by KF.")
                
            else:
                category_path, level, cost_cat = self.get_category_mainitem(main_item)

            
            self.output_df[f'category_path_keyword'] = self.output_df.apply(lambda row: category_path if row['product_name'] == self.product_name else row[f'category_path_keyword'], axis=1)

            self.output_df[f'cost_mainitem_category'] = self.output_df.apply(lambda row: cost_cat if row['product_name'] == self.product_name else row[f'cost_mainitem_category'], axis=1)

            if len(category_path)==0:
                category_path = self.get_product_category_sim()
            else:
                category_path = self.get_category_next(category_path, level)
        else:
            logging.info("No matching nouns found.")
            category_path = self.get_product_category_sim()
        
        return category_path
    
    
    def get_category_hardlogic(self):
        """Get the category of the product by check the existance of the words from hard logic in the product name.

        :returns: The category path of the product and the level till which the category is available.
        """
        matched_words = []
        text = self.product_name.lower()

        pattern_matches = self.database.fetch_patterns()

        for keyword in pattern_matches:
            pattern = keyword[0].lower()
            if re.search(pattern, text):
                matched_words.append(keyword[0])
                break

        if not matched_words:
            non_pattern_matches = self.database.fetch_hardlogic_words()
            for keyword in non_pattern_matches:
                pattern = r'\b' + re.escape(keyword[0].lower()) + r'\b'
                if re.search(pattern, text):
                    matched_words.append(keyword[0])
                    break
        
        if matched_words:
            main_word = matched_words[0]
            self.output_df[f'hard_logic_word'] = self.output_df.apply(lambda row: main_word if row['product_name'] == self.product_name else row[f'hard_logic_word'], axis=1)

            direct_match =  self.database.fetch_category_hardlogic(main_word)

            if direct_match:
                category_hl = []
                for value in direct_match:
                    if value is not None:
                        category_hl.append(value)
                # self.output_df[f'is_hallucination'] = self.output_df.apply(lambda row: 0 if row['product_name'] == self.product_name else row[f'is_hallucination'], axis=1)
                return ' > '.join(category_hl), len(category_hl) - 1
        else:
            return '', -1
        

    def get_category(self):
        """Get the category of the product by hard logic, KF and noun.

        :return: The category path of the product.
        """
        logging.info('Checking the hard logics for category.')

        category_path_hl , level = self.get_category_hardlogic()

        self.output_df[f'category_path_hardlogic'] = self.output_df.apply(lambda row: category_path_hl if row['product_name'] == self.product_name else row[f'category_path_hardlogic'], axis=1)
        
        
        if len(category_path_hl)>0:
            logging.info(f'Category found till Level {level+1} by hard logic.')
            category_path = self.get_category_next(category_path_hl, level)
        
        else:
            logging.info('No match found with the hard logic.')
            category_path = self.get_category_nouns()

        return category_path
    

    def parse_text_to_dict(self, text):
        """Convert the category and cost of the product into dictionary.

        :param text: The text that needs to be converted to dictionary.
        :return: Dictionary conatining the product category and cost.
        """
        parts = text.split(" > ")
        parsed_dict = {}
        for i, part in enumerate(parts):
            level_key = f"Level {i + 1}"
            parsed_dict[level_key] = part
    
        return parsed_dict

    
    def generator(self,product_id, product_name, product_description):
        """Main function that runs the whole process to generate the product category.
        
        :param product_id: The product ID of the product that needs to be categorized.
        :param product_name: The name of the product that needs to be categorized.
        :param product_description: The description of the product that needs to be categorized.
        :return: Category mapping of the product.
        """
        logging.info(f'Generating the category for "{product_name}".')
        self.product_embed = None
        self.product_embed_cost = 0
        self.output_df = pd.DataFrame(columns= ["product_id","product_name",'product_description','product_embed_cost',"hard_logic_word", "category_path_hardlogic","keywords_nouns","soft_logic_words","main_item","category_path_keyword","main_item_cost","embed_cost_nouns",'cost_mainitem_category',"options1","llm_output1","cost1","options2","llm_output2","cost2","options3","llm_output3","cost3","options4","llm_output4","cost4","options5","llm_output5","cost5","options6","llm_output6","cost6","options7","llm_output7","cost7","is_hallucination",'Level 1', "Level 2", "Level 3", "Level 4","Level 5", "Level 6","Level 7"])

        try:
            self.database = database()
            self.product_id = product_id
            self.product_name = product_name
            self.product_description = product_description
            self.cleaned_description = self.preprocess.remove_ingredients(self.preprocess.extract_text(product_description))

            self.output_df['product_id'] = [int(self.product_id)]
            self.output_df['product_name'] = [self.product_name]
            self.output_df['product_description'] = [self.cleaned_description]
            self.output_df[f'is_hallucination'] = self.output_df.apply(lambda row: 0 if row['product_name'] == self.product_name else row[f'is_hallucination'], axis=1)

            category_path = self.get_category()
            self.output_df['product_embed_cost'] = [self.product_embed_cost]
            if category_path is not None:
                output_dict = self.parse_text_to_dict(category_path)
                for key,val in output_dict.items():
                    self.output_df[key] = [val]

                self.database.update_table_from_df(self.product_id,self.output_df)
                print(output_dict)
                return output_dict
            else:
                self.database.update_table_from_df(self.product_id,self.output_df)
                logging.warning(f"Invalid category path returned for '{product_name}'.")
                return None
            
        except ValueError as e:
            raise ValueError(str(e))
        
        except Exception as e:
            logging.exception(f"Error extracting category. {e}")
            raise Exception(str(e))

        
    def exit_generator(self):
        """Exit the category_generator class and close the database connection."""
        self.database.close_connection()
