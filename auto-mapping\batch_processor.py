#!/usr/bin/env python
"""
Batch Processing for Category Mapping

This script provides batch processing capabilities with error resilience,
progress tracking, and comprehensive reporting.
"""

import os
import json
import time
from typing import Dict, List, Optional, Tuple
from datetime import datetime
from dotenv import load_dotenv

# Import CrewAI components
from crew_components import (
    category_mapping_crew,
    mapper_agent,
    validation_agent,
    CategoryHybridSearchTool
)

# Import error handling and database
from crew_components.error_handlers import (
    ProtectedExecution,
    wrap_tool_call,
    wrap_agent_execution,
    MappingError
)
from crew_components.logging_config import get_logger, ErrorCategory, LogLevel
from crew_components.database_handler import DatabaseHandler

# Load environment variables
load_dotenv()

# Initialize logger
logger = get_logger()


class BatchProcessor:
    """
    Handles batch processing of products with error resilience.
    """
    
    def __init__(self, max_retries: int = 3, batch_size: int = 100):
        """
        Initialize batch processor.
        
        Args:
            max_retries: Maximum retry attempts per product
            batch_size: Number of products to process in a batch
        """
        self.max_retries = max_retries
        self.batch_size = batch_size
        self.search_tool = None
        self.db_handler = None
        self.statistics = {
            'total': 0,
            'successful': 0,
            'failed': 0,
            'skipped': 0,
            'errors': []
        }
        
    def initialize(self) -> bool:
        """
        Initialize search tool and database connection.
        
        Returns:
            True if initialization successful
        """
        try:
            logger.log_event(
                LogLevel.INFO,
                "Initializing batch processor",
                category=ErrorCategory.CONFIGURATION
            )
            
            # Initialize search tool
            from auto_mapping import initialize_search_tool, setup_agents
            self.search_tool = initialize_search_tool()
            setup_agents(self.search_tool)
            
            # Initialize database handler
            self.db_handler = DatabaseHandler()
            self.db_handler.connect()
            
            logger.log_event(
                LogLevel.INFO,
                "Batch processor initialized successfully",
                category=ErrorCategory.CONFIGURATION
            )
            
            return True
            
        except Exception as e:
            logger.log_error(
                e,
                "Failed to initialize batch processor",
                category=ErrorCategory.CONFIGURATION
            )
            return False
            
    def cleanup(self):
        """Clean up resources."""
        if self.db_handler:
            self.db_handler.disconnect()
            
    def process_batch(self, products: List[Dict]) -> Dict:
        """
        Process a batch of products with error resilience.
        
        Args:
            products: List of product dictionaries
            
        Returns:
            Processing statistics
        """
        batch_stats = {
            'total': len(products),
            'successful': 0,
            'failed': 0,
            'skipped': 0,
            'errors': [],
            'start_time': datetime.now(),
            'end_time': None
        }
        
        logger.log_event(
            LogLevel.INFO,
            f"Starting batch processing for {len(products)} products",
            category=ErrorCategory.UNKNOWN,
            batch_size=len(products)
        )
        
        for index, product in enumerate(products, 1):
            product_id = product.get('product_id', 'unknown')
            
            try:
                # Process with isolated error handling
                with ProtectedExecution(
                    f"batch_product_{product_id}",
                    category=ErrorCategory.AGENT,
                    product_id=product_id,
                    raise_on_error=False
                ) as protected:
                    
                    logger.log_event(
                        LogLevel.INFO,
                        f"Processing product {index}/{len(products)}",
                        product_id=product_id,
                        product_name=product.get('product_name'),
                        progress=f"{index}/{len(products)}"
                    )
                    
                    # Process single product
                    from auto_mapping import process_single_product
                    result = process_single_product(
                        product,
                        db_handler=self.db_handler,
                        max_retries=self.max_retries
                    )
                    
                    if result:
                        batch_stats['successful'] += 1
                        logger.log_event(
                            LogLevel.INFO,
                            f"Product {product_id} mapped successfully",
                            product_id=product_id
                        )
                    else:
                        batch_stats['failed'] += 1
                        batch_stats['errors'].append({
                            'product_id': product_id,
                            'error': 'Mapping failed after retries'
                        })
                        
                    protected.set_result(result)
                    
            except Exception as e:
                # Isolated failure - continue with next product
                batch_stats['failed'] += 1
                batch_stats['errors'].append({
                    'product_id': product_id,
                    'error': str(e)[:200]
                })
                
                logger.log_error(
                    e,
                    f"Failed to process product {product_id}",
                    category=ErrorCategory.AGENT,
                    product_id=product_id
                )
                
                # Continue to next product
                continue
                
            # Optional: Add delay between products to avoid overwhelming APIs
            if index < len(products):
                time.sleep(0.5)  # 500ms delay
                
        batch_stats['end_time'] = datetime.now()
        batch_stats['duration'] = (batch_stats['end_time'] - batch_stats['start_time']).total_seconds()
        
        logger.log_event(
            LogLevel.INFO,
            f"Batch processing completed",
            category=ErrorCategory.UNKNOWN,
            **batch_stats
        )
        
        return batch_stats
        
    def process_unmapped_products(self, limit: Optional[int] = None) -> Dict:
        """
        Process all unmapped products from the database.
        
        Args:
            limit: Maximum number of products to process
            
        Returns:
            Overall processing statistics
        """
        if not self.db_handler:
            raise MappingError("Database handler not initialized")
            
        overall_stats = {
            'batches': 0,
            'total': 0,
            'successful': 0,
            'failed': 0,
            'start_time': datetime.now(),
            'end_time': None,
            'batch_results': []
        }
        
        try:
            # Get initial statistics
            initial_stats = self.db_handler.get_mapping_statistics()
            logger.log_event(
                LogLevel.INFO,
                "Starting unmapped product processing",
                category=ErrorCategory.UNKNOWN,
                **initial_stats
            )
            
            processed_count = 0
            batch_limit = limit if limit and limit < self.batch_size else self.batch_size
            
            while True:
                # Get next batch of unmapped products
                products = self.db_handler.get_unmapped_products(batch_limit)
                
                if not products:
                    logger.log_event(
                        LogLevel.INFO,
                        "No more unmapped products to process",
                        category=ErrorCategory.UNKNOWN
                    )
                    break
                    
                # Process the batch
                batch_stats = self.process_batch(products)
                
                # Update overall statistics
                overall_stats['batches'] += 1
                overall_stats['total'] += batch_stats['total']
                overall_stats['successful'] += batch_stats['successful']
                overall_stats['failed'] += batch_stats['failed']
                overall_stats['batch_results'].append(batch_stats)
                
                processed_count += batch_stats['total']
                
                # Check if we've reached the limit
                if limit and processed_count >= limit:
                    logger.log_event(
                        LogLevel.INFO,
                        f"Reached processing limit of {limit} products",
                        category=ErrorCategory.UNKNOWN
                    )
                    break
                    
                # Brief pause between batches
                time.sleep(2)
                
        except Exception as e:
            logger.log_error(
                e,
                "Error during batch processing",
                category=ErrorCategory.UNKNOWN
            )
            
        finally:
            # Get final statistics
            final_stats = self.db_handler.get_mapping_statistics()
            
            overall_stats['end_time'] = datetime.now()
            overall_stats['duration'] = (overall_stats['end_time'] - overall_stats['start_time']).total_seconds()
            overall_stats['final_db_stats'] = final_stats
            
            # Generate summary report
            self.generate_report(overall_stats, initial_stats, final_stats)
            
        return overall_stats
        
    def generate_report(self, stats: Dict, initial: Dict, final: Dict):
        """
        Generate a processing report.
        
        Args:
            stats: Processing statistics
            initial: Initial database statistics
            final: Final database statistics
        """
        report = []
        report.append("\n" + "=" * 60)
        report.append("BATCH PROCESSING REPORT")
        report.append("=" * 60)
        
        # Processing summary
        report.append(f"\nProcessing Summary:")
        report.append(f"  Duration: {stats['duration']:.2f} seconds")
        report.append(f"  Batches: {stats['batches']}")
        report.append(f"  Total Processed: {stats['total']}")
        report.append(f"  Successful: {stats['successful']} ({stats['successful']/max(stats['total'],1)*100:.1f}%)")
        report.append(f"  Failed: {stats['failed']} ({stats['failed']/max(stats['total'],1)*100:.1f}%)")
        
        # Database statistics
        report.append(f"\nDatabase Statistics:")
        report.append(f"  Before Processing:")
        report.append(f"    - Mapped: {initial['mapped']}")
        report.append(f"    - Failed: {initial['failed']}")
        report.append(f"    - Unmapped: {initial['unmapped']}")
        
        report.append(f"  After Processing:")
        report.append(f"    - Mapped: {final['mapped']} (+{final['mapped'] - initial['mapped']})")
        report.append(f"    - Failed: {final['failed']} (+{final['failed'] - initial['failed']})")
        report.append(f"    - Unmapped: {final['unmapped']} ({initial['unmapped'] - final['unmapped']} processed)")
        
        # Performance metrics
        if stats['duration'] > 0:
            throughput = stats['total'] / stats['duration']
            report.append(f"\nPerformance:")
            report.append(f"  Throughput: {throughput:.2f} products/second")
            report.append(f"  Avg Time/Product: {stats['duration']/max(stats['total'],1):.2f} seconds")
        
        report.append("=" * 60)
        
        # Print and log the report
        report_text = '\n'.join(report)
        print(report_text)
        
        logger.log_event(
            LogLevel.INFO,
            "Batch processing report generated",
            category=ErrorCategory.UNKNOWN,
            report=report_text
        )
        
        # Save report to file
        report_file = f"logs/batch_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        try:
            os.makedirs("logs", exist_ok=True)
            with open(report_file, 'w') as f:
                f.write(report_text)
            print(f"\nReport saved to: {report_file}")
        except Exception as e:
            logger.log_error(e, "Failed to save report file", category=ErrorCategory.UNKNOWN)


def main():
    """Main execution for batch processing."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Batch process product categorization')
    parser.add_argument('--limit', type=int, help='Maximum number of products to process')
    parser.add_argument('--batch-size', type=int, default=100, help='Batch size (default: 100)')
    parser.add_argument('--max-retries', type=int, default=3, help='Max retries per product (default: 3)')
    
    args = parser.parse_args()
    
    # Initialize processor
    processor = BatchProcessor(
        max_retries=args.max_retries,
        batch_size=args.batch_size
    )
    
    try:
        # Initialize resources
        if not processor.initialize():
            print("[ERROR] Failed to initialize batch processor")
            return 1
            
        # Process unmapped products
        stats = processor.process_unmapped_products(limit=args.limit)
        
        # Return success if any products were mapped
        return 0 if stats['successful'] > 0 else 1
        
    except Exception as e:
        logger.log_error(e, "Batch processing failed", category=ErrorCategory.UNKNOWN)
        print(f"[ERROR] Batch processing failed: {e}")
        return 1
        
    finally:
        processor.cleanup()


if __name__ == "__main__":
    import sys
    sys.exit(main())
