#!/usr/bin/env python
"""
Run parallel processor with a configurable number of test products.
Default: 10 products.
Usage:
  python auto-mapping/run_parallel.py [num_products]
"""
import sys
from typing import List, Dict

from parallel_processor import ParallelWorkflowOrchestrator

def make_test_products(n: int) -> List[Dict]:
    # Match the schema used by auto_mapping.process_single_product and agents
    # - product_id (str/int)
    # - product_name (str)
    # - product_description (str)
    # - url (str, optional)
    # - breadcrumbs (list[str], optional)
    items: List[Dict] = [
        {
            'product_id': 4832,
            'product_name': "ASDA Garden Scoop",
            'product_description': "This garden scoop is ideal for many tasks both in the garden and around your home",
            'url': 'https://groceries.asda.com/product/garden-tools-accessories/asda-garden-scoop/1000003352998',
            'breadcrumbs': ['Home & Entertainment', 'Garden & Outdoor', 'Gardening', 'Garden Tools & Accessories']
        },
        {
            'product_id': 4833,
            'product_name': "Organic Tomato Seeds",
            'product_description': "Premium organic tomato seeds for home gardening",
            'url': 'https://groceries.asda.com/product/seeds/organic-tomato-seeds/1000003352999',
            'breadcrumbs': ['Home & Entertainment', 'Garden & Outdoor', 'Gardening', 'Seeds & Bulbs']
        },
        {
            'product_id': 4834,
            'product_name': "Kitchen Knife Set",
            'product_description': "Professional 5-piece kitchen knife set with wooden block",
            'url': 'https://groceries.asda.com/product/kitchen/knife-set/1000003353000',
            'breadcrumbs': ['Home & Entertainment', 'Kitchen & Dining', 'Cooking & Baking', 'Knives & Cutting']
        },
        {
            'product_id': 4835,
            'product_name': "Wireless Bluetooth Headphones",
            'product_description': "High-quality wireless headphones with noise cancellation",
            'url': 'https://groceries.asda.com/product/electronics/headphones/1000003353001',
            'breadcrumbs': ['Electronics', 'Audio & Video', 'Headphones & Earphones']
        },
        {
            'product_id': 4836,
            'product_name': "Baby Formula Milk",
            'product_description': "Nutritious baby formula for infants 0-6 months",
            'url': 'https://groceries.asda.com/product/baby/formula/1000003353002',
            'breadcrumbs': ['Baby & Toddler', 'Baby Food & Milk', 'Baby Formula']
        },
        {
            'product_id': 4837,
            'product_name': "ASDA Garden Hose 15m",
            'product_description': "Durable 15 meter garden hose suitable for watering plants and lawns",
            'url': 'https://groceries.asda.com/product/garden-tools-accessories/asda-garden-hose/1000003353003',
            'breadcrumbs': ['Home & Entertainment', 'Garden & Outdoor', 'Gardening', 'Garden Tools & Accessories']
        },
        {
            'product_id': 4838,
            'product_name': "Stainless Steel Saucepan 20cm",
            'product_description': "Premium stainless steel saucepan ideal for everyday cooking",
            'url': 'https://groceries.asda.com/product/kitchen/saucepans/1000003353004',
            'breadcrumbs': ['Home & Entertainment', 'Kitchen & Dining', 'Cooking & Baking', 'Pots & Pans']
        },
        {
            'product_id': 4839,
            'product_name': "LED Desk Lamp",
            'product_description': "Adjustable LED desk lamp with brightness settings for home office",
            'url': 'https://groceries.asda.com/product/home-lighting/led-desk-lamp/1000003353005',
            'breadcrumbs': ['Home & Entertainment', 'Home Accessories', 'Lighting', 'Desk Lamps']
        },
        {
            'product_id': 4840,
            'product_name': "Gaming Mouse",
            'product_description': "Ergonomic gaming mouse with programmable buttons and RGB lighting",
            'url': 'https://groceries.asda.com/product/electronics/computers/gaming-mouse/1000003353006',
            'breadcrumbs': ['Electronics', 'Computers & Accessories', 'Mice & Keyboards']
        },
        {
            'product_id': 4841,
            'product_name': "Infant Diapers Size 2",
            'product_description': "Soft and absorbent nappies for infants weighing 3-6kg",
            'url': 'https://groceries.asda.com/product/baby/nappies/size-2/1000003353007',
            'breadcrumbs': ['Baby & Toddler', 'Nappies & Wipes', 'Nappies']
        },
        {
            'product_id': 4842,
            'product_name': "BBQ Charcoal Briquettes",
            'product_description': "Long-lasting charcoal briquettes for barbecues and grilling",
            'url': 'https://groceries.asda.com/product/garden-bbq/charcoal-briquettes/1000003353008',
            'breadcrumbs': ['Home & Entertainment', 'Garden & Outdoor', 'BBQ & Accessories', 'Charcoal']
        }
    ]
    return items[:n]



def main():
    try:
        n = int(sys.argv[1]) if len(sys.argv) > 1 else 10
    except ValueError:
        n = 10

    products = make_test_products(n)  
    results = ParallelWorkflowOrchestrator(agent_workers=2).process_products_parallel(products)

    # Print concise summary
    print("\n=== PARALLEL RUN SUMMARY ===")
    print(f"Products: {n}")
    print(f"Total time (ms): {results.get('orchestration_time_ms'):.2f}")
    print(f"Throughput (products/sec): {results.get('overall_throughput_per_second'):.2f}")
 

    agent = results.get('agent_results') or {}
    print("\nAgents:")
    print(f"  Throughput (products/sec): {agent.get('throughput_per_second', 0):.2f}")
    print(f"  Success: {agent.get('successful', 0)}/{agent.get('total_items', 0)}")

    # Print per-product category paths
    per_product = agent.get('results') or {}
    if per_product:
        print("\nPer-product category paths:")
        for pid, path in per_product.items():
            if isinstance(path, dict):
                levels = [path.get(f"level_{i}", "") for i in range(1, 8)]
                joined = " > ".join([lvl for lvl in levels if isinstance(lvl, str) and lvl.strip()])
                print(f"  - {pid}: {joined}")
            else:
                print(f"  - {pid}: {path}")


if __name__ == "__main__":
    main()
