# API Reference

## 📚 Complete API Documentation

This document provides comprehensive API reference for all components of the Category Mapping System.

## 🤖 CrewAI Agents

### Mapper Agent
```python
from crew_components.agents import mapper_agent

# Agent Properties
mapper_agent.role          # 'Product Category Mapper'
mapper_agent.goal          # Accuracy goal description
mapper_agent.backstory     # Expert background context
mapper_agent.tools         # [CategoryHybridSearchTool] (assigned dynamically)
mapper_agent.allow_delegation  # False
mapper_agent.verbose       # True
```

### Validation Agent  
```python
from crew_components.agents import validation_agent

# Agent Properties
validation_agent.role      # 'Mapping Validation Specialist'
validation_agent.goal      # Verification goal description
validation_agent.backstory # QA expert background context
validation_agent.tools     # [CategoryHybridSearchTool] (assigned dynamically)
validation_agent.allow_delegation  # False
validation_agent.verbose   # True
```

## 📋 CrewAI Tasks

### Mapping Task
```python
from crew_components.tasks import map_product_task

# Task Configuration
map_product_task.description    # 'Analyze product info: {product_info}...'
map_product_task.expected_output # 'JSON object with level_1 through level_7'
map_product_task.agent         # mapper_agent

# Usage
result = map_product_task.execute(inputs={'product_info': json.dumps(product)})
```

### Validation Task
```python
from crew_components.tasks import validate_mapping_task

# Task Configuration  
validate_mapping_task.description    # 'Review product and mapping...'
validate_mapping_task.expected_output # 'JSON with status, confidence_score, corrected_path'
validate_mapping_task.agent         # validation_agent
validate_mapping_task.context       # [map_product_task]

# Usage (automatically receives mapper context)
result = validate_mapping_task.execute()
```

## 🔍 Search Tools

### CategoryHybridSearchTool
```python
from crew_components.tools.category_hybrid_search import CategoryHybridSearchTool

# Initialization
tool = CategoryHybridSearchTool()

# Properties
tool.name          # 'category_hybrid_search'
tool.description   # 'Advanced category search using both keyword matching...'
tool.args_schema   # HybridSearchInput

# Usage
result = tool._run("gaming laptop high performance")
# Returns formatted string with search results
```

### CategoryHybridSearch (Core)
```python
from crew_components.tools.category_hybrid_search import CategoryHybridSearch

# Initialization
search = CategoryHybridSearch()

# Properties
search.semantic_available  # bool: True if FAISS embeddings loaded
search.keyword_tool       # CategoryHierarchyTool instance
search.embeddings_manager # CategoryEmbeddingsManager instance

# Methods
results = search.search(
    query="coffee beans premium",
    k=5,                    # Number of results
    use_semantic=True       # Enable semantic search
)
# Returns List[HybridSearchResult]

formatted = search.format_results(results, query)
# Returns formatted string for display
```

### HybridSearchResult
```python
from crew_components.tools.category_hybrid_search import HybridSearchResult

# Creation
result = HybridSearchResult(
    category={"path": "Electronics > Computers"},
    keyword_score=0.8,
    semantic_score=0.9
)

# Properties
result.category        # Dict: Category information
result.keyword_score   # float: Keyword match score (0-1)
result.semantic_score  # float: Semantic similarity score (0-1)
result.total_score     # float: Combined score (0.6*semantic + 0.4*keyword)

# Methods
str(result)           # Human-readable representation
```

## 🗄️ Database Operations

### DatabaseHandler
```python
from crew_components.database_handler import DatabaseHandler

# Initialization
db = DatabaseHandler()

# Properties
db.db_config     # Dict: Database connection parameters
db.conn          # psycopg2.Connection: Database connection
db.cursor        # psycopg2.Cursor: Database cursor

# Connection Management
db.connect()     # Establish connection
db.disconnect()  # Close connection

# Context Manager
with DatabaseHandler() as db:
    # Database operations
    pass

# Operations (if implemented)
db.update_mapping_status(product_id, category_path, confidence)
db.log_mapping_failure(product_id, error_message)
db.get_product_info(product_id)
db.get_mapping_statistics()
```

## ⚡ Performance Tools

### PerformanceProfiler
```python
from performance_profiler import get_profiler

# Get global profiler instance
profiler = get_profiler()

# Profile operations
with profiler.profile_operation("my_operation", custom_param="value"):
    # Your code here
    result = expensive_operation()

# Profile functions
@profile_function("function_name", param1="value1")
def my_function():
    # Function code
    pass

# Generate reports
report_file = profiler.generate_performance_report()

# Get system metrics
metrics = profiler.get_system_metrics()

# Clear metrics
profiler.clear_metrics()
```

### Performance Decorators
```python
# Database query profiling
@profiler.profile_database_query("SELECT", "SELECT * FROM table WHERE...")
def database_operation():
    return cursor.fetchall()

# Agent execution profiling
@profiler.profile_agent_execution("mapper_agent", "product_mapping")
def agent_operation():
    return agent.execute_task(task)
```

## 🚀 Parallel Processing

### ParallelFAISSProcessor
```python
from parallel_processor import ParallelFAISSProcessor

# Initialization
faiss_processor = ParallelFAISSProcessor(
    max_workers=4,      # Number of worker threads
    batch_size=20       # Queries per batch
)

# Batch search
results = faiss_processor.search_batch(
    queries=["query1", "query2", ...],
    k=5                 # Results per query
)

# Results format
{
    'total_queries': 20,
    'successful_queries': 20,
    'avg_time_per_query_ms': 764.92,
    'queries_per_second': 1.31,
    'results': {...},
    'errors': []
}
```

### ParallelAgentProcessor
```python
from parallel_processor import ParallelAgentProcessor

# Initialization
agent_processor = ParallelAgentProcessor(
    max_workers=2,      # Conservative for LLM calls
    batch_size=10       # Products per batch
)

# Process batch
batch_result = agent_processor.process_batch(products)

# BatchResult properties
batch_result.batch_id              # Unique batch identifier
batch_result.total_items           # Number of products processed
batch_result.successful            # Successful mappings
batch_result.failed               # Failed mappings
batch_result.processing_time_ms    # Total processing time
batch_result.throughput_per_second # Products per second
batch_result.errors               # List of errors
```

### ParallelWorkflowOrchestrator
```python
from parallel_processor import ParallelWorkflowOrchestrator

# Initialization
orchestrator = ParallelWorkflowOrchestrator(
    faiss_workers=4,    # FAISS search workers
    agent_workers=2,    # Agent processing workers
    db_pool_size=5      # Database connection pool
)

# Process products
results = orchestrator.process_products_parallel(
    products,
    enable_faiss_parallel=True,
    enable_agent_parallel=True
)

# Results format
{
    'orchestration_time_ms': 16083.73,
    'products_processed': 20,
    'overall_throughput_per_second': 1.24,
    'faiss_results': {...},
    'agent_results': {...},
    'database_results': {...},
    'performance_improvement': {
        'speedup_factor': 1.26,
        'time_saved_percent': 20.8
    }
}
```

## 🛡️ Error Handling

### Error Classes
```python
from crew_components.error_handlers import (
    MappingError,           # Base exception
    ToolExecutionError,     # Tool failures
    AgentExecutionError,    # Agent failures
    DatabaseError,          # Database failures
    APIError               # API failures
)

# Usage
try:
    risky_operation()
except ToolExecutionError as e:
    logger.log_error(e, "Tool failed", category=ErrorCategory.TOOL)
```

### Safe Execution Decorator
```python
from crew_components.error_handlers import safe_execution

@safe_execution("operation_name", ErrorCategory.TOOL, max_retries=3)
def risky_function():
    # Function that might fail
    return result

# Returns None if all retries fail
# Automatically logs errors and performance
```

### Protected Execution Context Manager
```python
from crew_components.error_handlers import ProtectedExecution

with ProtectedExecution(
    "operation_name",
    category=ErrorCategory.AGENT,
    product_id=123,
    raise_on_error=False
) as protected:
    
    result = perform_operation()
    protected.set_result(result)

# Check results
if protected.error:
    print(f"Error: {protected.error}")
else:
    print(f"Result: {protected.result}")
```

## 📝 Logging

### StructuredLogger
```python
from crew_components.logging_config import get_logger, LogLevel, ErrorCategory

logger = get_logger()

# Event logging
logger.log_event(
    level=LogLevel.INFO,
    message="Operation completed",
    category=ErrorCategory.TOOL,
    product_id=123,
    custom_field="custom_value"
)

# Error logging
logger.log_error(
    error=exception,
    message="Operation failed",
    category=ErrorCategory.DATABASE,
    product_id=123
)

# Performance logging
logger.log_performance(
    operation="database_query",
    duration_ms=150.5,
    category=ErrorCategory.DATABASE,
    success=True,
    rows_affected=10
)
```

### Log Levels and Categories
```python
# Log Levels
class LogLevel(Enum):
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

# Error Categories
class ErrorCategory(Enum):
    DATABASE = "DATABASE"
    API = "API"
    AGENT = "AGENT"
    TOOL = "TOOL"
    VALIDATION = "VALIDATION"
    NETWORK = "NETWORK"
    CONFIGURATION = "CONFIGURATION"
    UNKNOWN = "UNKNOWN"
```

## 🔄 Batch Processing

### BatchProcessor
```python
from batch_processor import BatchProcessor

# Initialization
processor = BatchProcessor(
    max_retries=3,      # Retry attempts per product
    batch_size=100      # Products per batch
)

# Setup
success = processor.initialize()  # Returns bool

# Properties
processor.statistics    # Dict: Processing statistics
processor.search_tool   # CategoryHybridSearchTool instance
processor.db_handler    # DatabaseHandler instance

# Processing
results = processor.process_batch(products)

# Cleanup
processor.cleanup()     # Close database connections
```

## 🎛️ Configuration

### Environment Configuration
```python
import os

# Database configuration
POSTGRES_HOST = os.getenv('POSTGRES_HOST', 'localhost')
POSTGRES_PORT = os.getenv('POSTGRES_PORT', '5433')
POSTGRES_DB = os.getenv('POSTGRES_DB', 'aicategorymapping')
POSTGRES_USER = os.getenv('POSTGRES_USER', 'cat_manager')
POSTGRES_PASSWORD = os.getenv('POSTGRES_PASSWORD')

# AI configuration
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')

# Performance configuration
BATCH_SIZE = int(os.getenv('BATCH_SIZE', '100'))
MAX_RETRIES = int(os.getenv('MAX_RETRIES', '3'))
LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
```

## 🔧 Utility Functions

### Main Processing Functions
```python
# Single product processing
from auto_mapping import process_single_product

result = process_single_product(
    product_data,           # Dict: Product information
    db_handler=None,        # Optional: DatabaseHandler instance
    max_retries=3          # int: Maximum retry attempts
)
# Returns: Dict with mapping result or None

# Search tool initialization
from auto_mapping import initialize_search_tool, setup_agents

search_tool = initialize_search_tool()  # Returns CategoryHybridSearchTool
mapper, validator = setup_agents(search_tool)  # Returns wrapped agents
```

### Performance Analysis
```python
# Performance analysis
from analyze_performance import PerformanceAnalyzer

analyzer = PerformanceAnalyzer()

# FAISS benchmarking
faiss_results = analyzer.benchmark_faiss_search(queries, k=5)

# Database benchmarking  
db_results = analyzer.benchmark_database_queries(query_count=50)

# Agent benchmarking
agent_results = analyzer.benchmark_agent_execution(test_products)

# Memory analysis
memory_results = analyzer.analyze_memory_usage()

# Comprehensive analysis
analysis_file = analyzer.run_comprehensive_analysis()
```

### FAISS Optimization
```python
from faiss_optimizer import FAISSOptimizer

optimizer = FAISSOptimizer(embeddings_dir="embeddings")

# Benchmark index types
benchmark_results = optimizer.benchmark_index_types(test_queries, k=5)

# Get optimization recommendations
optimization_report = optimizer.optimize_index()

# Recommendations include:
# - Best performing index type
# - Memory optimization strategies
# - Performance tuning suggestions
```

## 📊 Data Models

### Product Data Model
```python
product = {
    "product_id": str,      # Required: Unique product identifier
    "product_name": str,    # Required: Product name
    "breadcrumbs": str,     # Optional: Category breadcrumbs
    "product_info": str,    # Optional: Additional product information
    "product_description": str  # Optional: Detailed description
}
```

### Mapping Result Model
```python
mapping_result = {
    "product_id": str,           # Product identifier
    "status": str,               # "success", "warning", "error"
    "category_path": str,        # Full category path
    "confidence": float,         # Confidence score (0.0-1.0)
    "level_1": str,             # Category level 1
    "level_2": str,             # Category level 2
    # ... through level_7
    "timestamp": str,            # ISO timestamp
    "processing_time_ms": float, # Processing duration
    "error": str                 # Error message (if status="error")
}
```

### Performance Metrics Model
```python
performance_metrics = {
    "operation_name": str,
    "start_time": float,
    "end_time": float,
    "duration_ms": float,
    "cpu_percent": float,
    "memory_mb": float,
    "memory_peak_mb": float,
    "thread_count": int,
    "success": bool,
    "error_message": str,
    "custom_metrics": dict
}
```

## 🔌 Integration APIs

### Crew Orchestration
```python
from crew_components.crew import category_mapping_crew

# Execute complete workflow
result = category_mapping_crew.kickoff(inputs={
    'product_info': json.dumps(product_data)
})

# Access result
if hasattr(result, 'raw'):
    parsed_result = json.loads(result.raw)
else:
    parsed_result = result
```

### Tool Wrapper Functions
```python
# Wrap tools with error handling
from crew_components.error_handlers import wrap_tool_call

wrapped_tool = wrap_tool_call(tool_instance, method_name="_run")

# Wrap agents with error handling  
from crew_components.error_handlers import wrap_agent_execution

wrapped_agent = wrap_agent_execution(agent_instance)
```

## 📈 Monitoring APIs

### System Metrics
```python
from performance_profiler import get_profiler

profiler = get_profiler()

# Get current system metrics
system_metrics = profiler.get_system_metrics()
# Returns:
{
    "timestamp": "2025-08-29T23:00:00",
    "cpu_percent": 15.2,
    "memory_mb": 156.8,
    "memory_percent": 12.5,
    "thread_count": 8,
    "open_files": 23,
    "connections": 5,
    "system_cpu_percent": 25.1,
    "system_memory_percent": 68.4,
    "system_disk_usage": 45.2
}
```

### Continuous Profiling
```python
# Start continuous profiling
profile_file = profiler.start_continuous_profiling()

# Your operations here
perform_intensive_operations()

# Stop and generate report
report_file = profiler.stop_continuous_profiling()
```

## 🔧 Configuration APIs

### Index Configuration
```python
from faiss_optimizer import IndexOptimizationConfig

# HNSW configuration (fastest)
config = IndexOptimizationConfig(
    index_type="HNSW",
    nlist=32,           # M parameter
    use_gpu=False
)

# IVFPQ configuration (memory efficient)
config = IndexOptimizationConfig(
    index_type="IVFPQ",
    nlist=100,          # Number of clusters
    nprobe=10,          # Clusters to search
    m=8,                # Subquantizers
    nbits=8,            # Bits per subquantizer
    memory_map=True     # Enable memory mapping
)

# Convert to dictionary
config_dict = config.to_dict()
```

### Parallel Processing Configuration
```python
# Configure parallel processors
faiss_config = {
    'max_workers': 4,
    'batch_size': 20
}

agent_config = {
    'max_workers': 2,
    'batch_size': 10
}

db_config = {
    'connection_pool_size': 5
}
```

## 🚨 Error Handling APIs

### Error Categories and Types
```python
# Import error types
from crew_components.error_handlers import (
    MappingError,           # Base error
    ToolExecutionError,     # Tool-specific errors
    AgentExecutionError,    # Agent-specific errors  
    DatabaseError,          # Database-specific errors
    APIError               # API-specific errors
)

# Import error categories
from crew_components.logging_config import ErrorCategory

# Usage in error handling
try:
    risky_operation()
except ToolExecutionError as e:
    logger.log_error(e, "Tool failed", category=ErrorCategory.TOOL)
except DatabaseError as e:
    logger.log_error(e, "Database failed", category=ErrorCategory.DATABASE)
```

## 🧪 Testing APIs

### Test Fixtures
```python
# Import test fixtures
from tests.conftest import (
    mock_db_connection,
    mock_pg_search_tool,
    sample_product_data,
    expected_mapping_results,
    temp_test_dir
)

# Use in tests
def test_with_fixtures(sample_product_data, mock_db_connection):
    assert len(sample_product_data) == 3
    mock_db_connection.cursor.assert_called()
```

### Custom Test Utilities
```python
# Custom assertions
from tests.conftest import assert_mapping_result

def test_mapping_result(assert_mapping_result):
    result = {"product_id": "123", "status": "success", "confidence": 0.95}
    expected = {"product_id": "123", "status": "success"}
    assert_mapping_result(result, expected)
```

## 📊 Batch Processing APIs

### Batch Statistics
```python
# Access batch statistics
processor = BatchProcessor()

stats = processor.statistics
# Returns:
{
    'total': 0,
    'successful': 0,
    'failed': 0,
    'skipped': 0,
    'errors': []
}
```

### Batch Result Processing
```python
# Process batch results
results = processor.process_batch(products)

# Access results
total_processed = results['total']
success_rate = results['successful'] / results['total'] * 100
error_count = len(results['errors'])

# Handle errors
for error in results['errors']:
    print(f"Product {error['product_id']}: {error['error']}")
```

---

For usage examples, see [README](README.md) and [Performance Guide](performance.md).
