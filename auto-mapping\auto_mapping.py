#!/usr/bin/env python
"""
Auto Mapping Main Script

This script orchestrates the CrewAI agents to map products to categories
using both keyword and semantic search capabilities.
"""

import os
import json
from typing import Dict, Optional, List
from dotenv import load_dotenv
from crewai import Crew, Process
from crew_components.tasks import map_product_task, validate_mapping_task

# Import CrewAI components
from crew_components import (
    mapper_agent,
    validation_agent,
    CategorySemanticSearchTool
)

# Import error handling
from crew_components.error_handlers import (
    ProtectedExecution,
    wrap_tool_call,
    wrap_agent_execution,
    MappingError
)
from crew_components.logging_config import get_logger, ErrorCategory, LogLevel
from crew_components.database_handler import DatabaseHandler

# Load environment variables
load_dotenv()

# Initialize logger
logger = get_logger()


def initialize_search_tool():
    """Initialize and configure the semantic search tool with error handling."""
    print("=" * 60)
    print("INITIALIZING CATEGORY MAPPING SYSTEM")
    print("=" * 60)
    
    with ProtectedExecution(
        "search_tool_initialization",
        category=ErrorCategory.TOOL,
        raise_on_error=True
    ) as protected:
        try:
            # Create semantic search tool
            search_tool = CategorySemanticSearchTool()
            
            # Wrap the tool with error handling
            search_tool = wrap_tool_call(search_tool)
            
            # Log successful initialization
            logger.log_event(
                LogLevel.INFO,
                "Search tool initialized successfully",
                category=ErrorCategory.CONFIGURATION
            )
            
            # Check capabilities
            if search_tool.search_tool.semantic_available:
                print("[OK] Semantic search ready:")
                stats = search_tool.search_tool.embeddings_manager.get_statistics()
                print(f"     - Semantic search: {stats['num_embeddings']} embeddings")
                print(f"     - Storage: {stats['storage_size_mb']} MB")
                
                logger.log_event(
                    LogLevel.INFO,
                    "Semantic search enabled",
                    category=ErrorCategory.CONFIGURATION,
                    num_embeddings=stats['num_embeddings']
                )
            else:
                # Fail fast if embeddings are not available
                msg = (
                    "Embeddings not available. Set EMBEDDINGS_DIR in auto-mapping/.env "
                    "to the absolute path of your embeddings directory or run "
                    "'python auto-mapping/generate_embeddings.py' to generate them."
                )
                logger.log_error(
                    Exception(msg),
                    "Semantic search unavailable - aborting initialization",
                    category=ErrorCategory.CONFIGURATION
                )
                print(f"[ERROR] {msg}")
                raise MappingError(msg)
            
            protected.set_result(search_tool)
            return search_tool
            
        except Exception as e:
            logger.log_error(
                e,
                "Failed to initialize search tool",
                category=ErrorCategory.TOOL
            )
            print(f"[ERROR] Failed to initialize search tool: {e}")
            print("\nTroubleshooting:")
            print("1. Ensure embeddings files exist. Run 'python auto-mapping/generate_embeddings.py' if needed.")
            print("2. Check .env file for EMBEDDINGS_DIR and OPENAI_API_KEY.")
            raise MappingError("Search tool initialization failed") from e


def setup_agents(search_tool):
    """Assign the search tool to agents with error handling."""
    with ProtectedExecution(
        "agent_setup",
        category=ErrorCategory.AGENT,
        raise_on_error=True
    ) as protected:
        # Wrap agents with error handling
        wrapped_mapper = wrap_agent_execution(mapper_agent)
        wrapped_validator = wrap_agent_execution(validation_agent)
        
        # Assign tools
        wrapped_mapper.tools = [search_tool]
        wrapped_validator.tools = [search_tool]
        
        logger.log_event(
            LogLevel.INFO,
            "Agents configured with search tool",
            category=ErrorCategory.CONFIGURATION,
            agents=["mapper_agent", "validation_agent"]
        )
        
        print("[OK] Search tool assigned to agents")
        print("=" * 60)
        
        protected.set_result((wrapped_mapper, wrapped_validator))
        return wrapped_mapper, wrapped_validator

def get_map_data_from_output(mapper_result):
    mapper_data = None
    if hasattr(mapper_result, 'pydantic') and getattr(mapper_result, 'pydantic') is not None:
        mapper_data = mapper_result.pydantic.model_dump()
    elif mapper_result and isinstance(mapper_result.raw, str) and mapper_result.raw.strip().startswith('{'):
        try:
            mapper_data = json.loads(mapper_result.raw)
        except json.JSONDecodeError:
            pass  # Keep mapper_data as None
    elif isinstance(mapper_result, dict):
        mapper_data = mapper_result
    return mapper_data

def get_validation_data_from_output(validation_result):
    validation_data = None
    if hasattr(validation_result, 'pydantic') and getattr(validation_result, 'pydantic') is not None:
        validation_data = validation_result.pydantic.model_dump()
    elif isinstance(validation_result, dict):
        validation_data = validation_result
    return validation_data


def process_single_product(product_data: Dict, 
                           crew: Crew,
                           max_retries: int = 2) -> Optional[Dict]:
    """
    Process a single product with retry logic and database updates.
    
    Args:
        product_data: Product information dictionary 
        max_retries: Maximum number of retry attempts
        crew: The pre-configured CrewAI instance to use for processing.
    
    Returns:
        Mapping result or None if failed
    """
    product_id = product_data.get('product_id', 'unknown')


    retry_feedback = ""  # Initialize feedback for retries
    last_error = None
    for attempt in range(max_retries):
        try:
            with ProtectedExecution(
                f"product_mapping_{product_id}",
                category=ErrorCategory.AGENT,
                product_id=product_id,
                raise_on_error=False
            ) as protected:
                
                # Create inputs for the crew
                inputs = {
                    'product_info': json.dumps(product_data),
                    'retry_feedback': retry_feedback
                }

                # Ensure tasks are correctly associated with agents in the crew.
                # This is good practice as tasks can be shared across crews.
                crew.tasks[0].agent = crew.agents[0]  # map_product_task -> mapper_agent
                crew.tasks[1].agent = crew.agents[1]  # validate_mapping_task -> validation_agent

                # Execute the full crew
                try:
                    validation_result = crew.kickoff(inputs=inputs)
                    
                except Exception as crew_error:
                    logger.log_error(
                        crew_error,
                        f"Crew execution failed: {str(crew_error)}",
                        category=ErrorCategory.AGENT,
                        product_id=product_id
                    )
                    print(f"[ERROR] Crew execution failed: {crew_error}")
                    raise

                # Parse validation result
                validation_data = get_validation_data_from_output(validation_result)
                # Get mapper result from the task output of the current crew instance
                # to avoid race conditions in parallel execution.
                mapper_data = get_map_data_from_output(crew.tasks[0].output)

                # Decide final category_path using validator status
                category_path = None
                try:
                    if isinstance(validation_data, dict) and validation_data.get('status') == 'approved':
                        if isinstance(mapper_data, dict):
                            category_path = {
                                'level_1': mapper_data.get('level_1', ''),
                                'level_2': mapper_data.get('level_2', ''),
                                'level_3': mapper_data.get('level_3', ''),
                                'level_4': mapper_data.get('level_4', ''),
                                'level_5': mapper_data.get('level_5', ''),
                                'level_6': mapper_data.get('level_6', ''),
                                'level_7': mapper_data.get('level_7', '')
                            }
                    elif isinstance(validation_data, dict) and validation_data.get('status') == 'rejected':
                        corrected = validation_data.get('corrected_path')
                        if isinstance(corrected, list) and len(corrected) >= 1:
                            # Pad corrected path to 7 levels
                            padded_corrected = corrected + [''] * (7 - len(corrected))
                            category_path = {f'level_{i+1}': padded_corrected[i] for i in range(7)}
                except Exception as e:
                    logger.log_error(e, "Failed to compose final category path", category=ErrorCategory.AGENT, product_id=product_id)

                # Check if the mapping is valid (at least level_1 is present)
                is_mapping_valid = category_path and isinstance(category_path, dict) and category_path.get('level_1')


                # If mapping is not valid, raise an error to trigger a retry
                if not is_mapping_valid:
                    # This will be caught by the outer exception handler and trigger a retry
                    raise MappingError(f"Agent returned an empty or invalid category mapping: {category_path or 'None'}")

                protected.set_result(category_path)
                return category_path

        except Exception as e:
            logger.log_error(
                e,
                f"Mapping attempt {attempt + 1} failed",
                category=ErrorCategory.AGENT,
                product_id=product_id,
                attempt=attempt + 1,
                max_retries=max_retries
            )
            last_error = e

            # Prepare feedback for the NEXT attempt
            if isinstance(e, MappingError):
                feedback_message = (
                    "\n\n--- PREVIOUS ATTEMPT FEEDBACK ---\n"
                    f"On your last attempt, you produced an invalid result. The error was: '{e}'.\n"
                    "This is not acceptable. You MUST provide a valid category path with at least 'level_1' populated.\n"
                    "Review the product information and your search strategy. Ensure you are selecting a valid category from the search tool results.\n"
                    "Do NOT return an empty or malformed result again."
                    "\n----------------------------------\n"
                )
            else:
                feedback_message = (
                    "\n\n--- PREVIOUS ATTEMPT FEEDBACK ---\n"
                    f"Your previous attempt failed with an error: {str(e)}.\n"
                    "This might have been a tool usage error or a formatting problem.\n"
                    "Please review the instructions carefully and try again. Pay close attention to the tool inputs and the required final JSON format."
                    "\n----------------------------------\n"
                )
            retry_feedback = feedback_message  # Set for the next loop iteration
            
            
            if attempt < max_retries - 1:
                # Wait before retry (exponential backoff)
                import time
                wait_time = 2 ** attempt
                
                time.sleep(wait_time)

    logger.log_error(
        last_error, f"Product mapping failed after {max_retries} attempts.",
        category=ErrorCategory.AGENT, product_id=product_id
    )
    return None


def process_multiple_products(
    products: List[Dict],  
    wrapped_mapper=None, 
    wrapped_validator=None,
    memory_reset_interval: int = 100) -> Dict:
    """
    Process multiple products with error isolation.
    
    Args:
        products: List of product dictionaries
        db_handler: Database handler for updates
        wrapped_mapper: The pre-configured mapper agent
        wrapped_validator: The pre-configured validation agent
        memory_reset_interval: How often to reset the crew's memory (in number of products).
        
    Returns:
        Processing statistics
    """
    stats = {
        'total': len(products),
        'successful': 0,
        'failed': 0,
        'results': []
    }
     
    
    # Create a single crew for this batch run
    batch_crew = Crew(
        agents=[wrapped_mapper, wrapped_validator],
        tasks=[map_product_task, validate_mapping_task],
        memory=True,
        share_crew=True,
        process=Process.sequential,
        verbose=True
    )

    for index, product in enumerate(products, 1):
        product_id = product.get('product_id', 'unknown')
        
        print(f"\n[{index}/{len(products)}] Processing: {product.get('product_name', 'Unknown')}")
        
        try:
            result = process_single_product(
                product, 
                crew=batch_crew,
                max_retries=2
            )
            
            if result:
                stats['successful'] += 1
                stats['results'].append({
                    'product_id': product_id,
                    'status': 'success',
                    'result': result
                })
            else:
                stats['failed'] += 1
                stats['results'].append({
                    'product_id': product_id,
                    'status': 'failed',
                    'error': 'Mapping failed after retries'
                })
                
        except Exception as e:
            # Log but don't stop processing
            logger.log_error(
                e,
                f"Error processing product {product_id}",
                category=ErrorCategory.AGENT,
                product_id=product_id
            )
            
            stats['failed'] += 1
            stats['results'].append({
                'product_id': product_id,
                'status': 'error',
                'error': str(e)[:200]
            })
            
        finally:
            # Reset memory periodically to prevent context window overflow
            if index % memory_reset_interval == 0 and index < len(products):
                print(f"[MemoryManager] Resetting memory after processing product {index}/{len(products)}.")
                batch_crew.reset_memories(command_type='all')
            
    # Print summary
    print("\n" + "=" * 60)
    print("BATCH PROCESSING SUMMARY")
    print("=" * 60)
    print(f"Total: {stats['total']}")
    print(f"Successful: {stats['successful']} ({stats['successful']/max(stats['total'],1)*100:.1f}%)")
    print(f"Failed: {stats['failed']} ({stats['failed']/max(stats['total'],1)*100:.1f}%)")
    print("=" * 60)
    
    return stats


def main():
    """Main execution function with comprehensive error handling."""

    try:
        # Log system startup
        logger.log_event(
            LogLevel.INFO,
            "Category mapping system starting",
            category=ErrorCategory.CONFIGURATION
        )
        
        # Initialize search tool
        search_tool = initialize_search_tool()

        # Setup agents with the tool
        wrapped_mapper, wrapped_validator = setup_agents(search_tool)
 

        # Create the crew for the main execution
        main_crew = Crew(
            agents=[wrapped_mapper, wrapped_validator],
            tasks=[map_product_task, validate_mapping_task],
            memory=True,
            share_crew=True,
            process=Process.sequential,
            verbose=True
        )
        # Example products to categorize (batch processing enabled)
        products_to_categorize = [
        #      {
        #     'product_id': 4833,
        #     'product_name': "Organic Tomato Seeds",
        #     'product_description': "Premium organic tomato seeds for home gardening",
        #     'url': 'https://groceries.asda.com/product/seeds/organic-tomato-seeds/1000003352999',
        #     'breadcrumbs': ['Home & Entertainment', 'Garden & Outdoor', 'Gardening', 'Seeds & Bulbs']
        # } 
            # {
            #     'product_id': 4834,
            #     'product_name': "Kitchen Knife Set",
            #     'product_description': "Professional 5-piece kitchen knife set with wooden block",
            #     'url': 'https://groceries.asda.com/product/kitchen/knife-set/1000003353000',
            #     'breadcrumbs': ['Home & Entertainment', 'Kitchen & Dining', 'Cooking & Baking', 'Knives & Cutting']
            # },
            # {
            #     'product_id': 4835,
            #     'product_name': "Wireless Bluetooth Headphones",
            #     'product_description': "High-quality wireless headphones with noise cancellation",
            #     'url': 'https://groceries.asda.com/product/electronics/headphones/1000003353001',
            #     'breadcrumbs': ['Electronics', 'Audio & Video', 'Headphones & Earphones']
            # },
            {
                'product_id': 4836,
                'product_name': "Baby Formula Milk",
                'product_description': "Nutritious baby formula for infants 0-6 months",
                'url': 'https://groceries.asda.com/product/baby/formula/1000003353002',
                'breadcrumbs': ['Baby & Toddler', 'Baby Food & Milk', 'Baby Formula']
            }
        ]
        
        if len(products_to_categorize) == 1:
            # Single product processing
            product = products_to_categorize[0]
            
            print("\nSTARTING CATEGORY MAPPING")
            print("-" * 60)
            print(f"Product: {product['product_name']}")
            print(f"Description: {product['product_description']}")
            if product.get('breadcrumbs'):
                print(f"Breadcrumbs: {'> '.join(product['breadcrumbs'])}")
            print("-" * 60)
            
            # Process the product with retry logic
            result = process_single_product(
                product, 
                crew=main_crew,
                max_retries=2
            )
            
            if result:
                print("\n" + "=" * 60)
                print("MAPPING COMPLETE")
                print("=" * 60)
                print("Final Result:")
                print(result)
                
                logger.log_event(
                    LogLevel.INFO,
                    "Mapping completed successfully",
                    product_id=product['product_id'],
                    result=str(result)[:500]
                )
            else:
                print("\n" + "=" * 60)
                print("MAPPING FAILED")
                print("=" * 60)
                print("Unable to map product after all retry attempts")
                
                logger.log_event(
                    LogLevel.ERROR,
                    "Mapping failed after all attempts",
                    product_id=product['product_id']
                )
        else:
            # Batch processing
            print(f"\nSTARTING BATCH PROCESSING FOR {len(products_to_categorize)} PRODUCTS")
            print("=" * 60)
            
            stats = process_multiple_products(
                products_to_categorize, 
                wrapped_mapper=wrapped_mapper, 
                wrapped_validator=wrapped_validator
            )
            
            logger.log_event(
                LogLevel.INFO,
                "Batch processing completed",
                category=ErrorCategory.UNKNOWN,
                **stats
            )
            
    except MappingError as e:
        logger.log_error(e, "Mapping system error", category=ErrorCategory.UNKNOWN)
        print(f"\n[ERROR] System error: {e}")
        exit(1)
    except Exception as e:
        logger.log_error(e, "Unexpected error", category=ErrorCategory.UNKNOWN)
        print(f"\n[ERROR] Unexpected error: {e}")
        exit(1)
     
        logger.log_event(
            LogLevel.INFO,
            "Category mapping system shutting down",
            category=ErrorCategory.CONFIGURATION
        )


if __name__ == "__main__":
    main()
