# Category Mapping with CrewAI

An intelligent multi-agent system for mapping products to a 7-level category hierarchy using CrewAI, combining keyword search with semantic understanding through embeddings.

## 🏗️ Project Structure

```
auto-mapping/
├── crew_components/           # All CrewAI components
│   ├── agents.py             # Mapper and Validation agents
│   ├── tasks.py              # Mapping and validation tasks
│   ├── crew.py               # Crew orchestration
│   ├── schemas.py            # Data models (Pydantic)
│   └── tools/                # Search and embedding tools
│       ├── category_hierarchy_tool.py      # Keyword-based search
│       ├── category_embeddings_manager.py  # Embeddings management
│       ├── category_hybrid_search.py       # Hybrid search (keyword + semantic)
│       └── generate_embeddings.py          # Embeddings generation utility
├── embeddings/               # Stored embeddings (local)
│   ├── category_embeddings.npz    # Compressed embeddings
│   ├── category_metadata.json     # Category metadata
│   ├── embeddings.faiss           # FAISS index for fast search
│   └── config.json               # Configuration
├── auto_mapping.py          # Main entry point
├── generate_embeddings.py   # Standalone embeddings generator
├── requirements.txt         # Python dependencies
└── .env                    # Environment variables

```

## 🚀 Quick Start

### 1. Setup Environment

```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Configure Environment Variables

Copy `environment_template.txt` to `.env` and add your credentials:

```env
# Database credentials (via SSH tunnel)
POSTGRES_USER=your_user
POSTGRES_PASSWORD=your_password
POSTGRES_HOST=localhost
POSTGRES_PORT=5433
POSTGRES_DB=aicategorymapping

# API Keys for semantic search
OPENAI_API_KEY=your_openai_key
# or
GOOGLE_API_KEY=your_google_key
```

### 3. Start SSH Tunnel (if using remote database)

```bash
python ../ssh_tunnel_database.py
```

### 4. Generate Embeddings (One-time setup)

```bash
python generate_embeddings.py
```

This creates embeddings for all 3,424 categories (~$0.0014 cost).

### 5. Run Category Mapping

```bash
python auto_mapping.py
```

## 🎯 Features

### Hybrid Search System
- **Keyword Search**: Fast pattern matching on category names
- **Semantic Search**: Understanding meaning using embeddings
- **Combined Scoring**: 60% semantic + 40% keyword for best results

### Multi-Agent Architecture
- **Mapper Agent**: Maps products to categories using search tools
- **Validation Agent**: Validates and corrects mappings
- **Sequential Process**: Ensures quality through two-stage validation

### Local Embeddings
- Stored locally for fast access (<10ms search time)
- Works offline once generated
- ~53MB total storage

## 📊 Performance

- **Categories**: 3,424 unique paths
- **Search Time**: <10ms per query
- **Embeddings Cost**: ~$0.0014 (one-time)
- **Accuracy**: Improved with semantic understanding

## 🛠️ Development

### Testing Search Capabilities

```python
from crew_components.tools import CategoryHybridSearchTool

tool = CategoryHybridSearchTool()
result = tool._run("garden scoop tool")
print(result)
```

### Regenerating Embeddings

```bash
python generate_embeddings.py
# Follow prompts to regenerate
```

### Running Without Semantic Search

The system works in keyword-only mode if embeddings are not available:
1. Delete `embeddings/` directory
2. Run `auto_mapping.py` (will warn about missing embeddings)

## 📝 Data Schemas

### Input: ProductMappingRequest
```python
{
    "product_id": 123,
    "title": "Product Name",
    "description": "Product description",
    "url": "https://...",
    "breadcrumbs": ["Level1", "Level2", ...]
}
```

### Output: ProductMappingResponse
```python
{
    "product_id": 123,
    "level_1": "Category Level 1",
    "level_2": "Category Level 2",
    # ... up to level_7
}
```

### Validation: ValidationResponse
```python
{
    "status": "approved/rejected",
    "confidence_score": 0.85,
    "corrected_path": {...}  # if rejected
}
```

## 🔧 Troubleshooting

### Database Connection Issues
- Ensure SSH tunnel is running
- Check `.env` credentials
- Verify PostgreSQL port (5433 for tunnel)

### Embeddings Not Working
- Check OPENAI_API_KEY in `.env`
- Run `python generate_embeddings.py`
- Verify `embeddings/` directory exists

### Agent Tool Errors
- Agents may need a few attempts to learn tool format
- Tool accepts simple string queries
- Check logs for detailed error messages

## 📚 Key Components

### CategoryHybridSearchTool
Main search tool combining keyword and semantic search. Used by agents to find matching categories.

### CategoryEmbeddingsManager
Manages generation, storage, and retrieval of category embeddings using OpenAI's text-embedding-3-small model.

### CrewAI Agents
- **Mapper Agent**: Analyzes product info and proposes category path
- **Validation Agent**: Reviews and corrects proposed mappings

## 🚦 Status Indicators

- `[OK]` - Component initialized successfully
- `[WARNING]` - Running with reduced capabilities
- `[ERROR]` - Component failed to initialize

## 📄 License

[Your License Here]

## 👥 Contributors

[Your Team Here]