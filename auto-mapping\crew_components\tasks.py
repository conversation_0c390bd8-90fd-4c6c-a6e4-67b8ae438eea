from crewai import Task
from .agents import mapper_agent, validation_agent
from .output_schemas import CategoryMapping, ValidationResult

# Mapping Task
map_product_task = Task(
    description=(
        'Map the product to the most accurate category path that EXISTS in the database.\n'
        'Product Info: {product_info}.\n'
        '{retry_feedback}'  # This will contain feedback on previous failed attempts.

        'CRITICAL RULES: '
        '1. PRODUCT NAME IS THE HIGHEST PRIORITY - Focus primarily on the actual product name, not breadcrumbs or URL '
        '2. You MUST use the category_hybrid_search tool to find valid database categories '
        '3. LIMIT search attempts to maximum 3 different queries to avoid excessive API calls '
        '4. If you receive feedback about a previous failed attempt, learn from it and correct your approach. '
        '5. If no categories are found after 3 search attempts, use breadcrumbs as fallback. '
        '6. Do NOT copy breadcrumbs directly. Use it to understand the product and create a search query. '
        '7. Create a search query from the product info, use the search tool, and select the best match from the results. '
        '8. If you are not completely confident in the search results, do NOT make up a category. Return an empty JSON object instead. \n\n'

        'TOOL USAGE INSTRUCTIONS:\n'
        'When calling category_hybrid_search, pass ONLY a simple string query:\n\n'

        'CORRECT examples:\n'
        '- category_hybrid_search({"query": "organic tomato seeds"})\n'
        '- category_hybrid_search({"query": "garden tools"})\n\n'

        'SEARCH STRATEGY (PRIORITIZE PRODUCT NAME):\n'
        '1. FIRST: Use the exact product name or key words from product name (e.g., "tomato seeds" for "Organic Tomato Seeds")\n'
        '2. SECOND: Try broader category terms if no results (e.g., "seeds" or "vegetable seeds")\n'
        '3. THIRD: Try one more specific variation\n'
        '4. LAST RESORT: Use breadcrumbs as fallback only if search tool returns no results\n\n'

        'FINAL OUTPUT REQUIREMENT:\n'
        'After using the search tool, you MUST return ONLY the JSON object with category levels. '
        'Do NOT return tool calls, search results, or explanations in your final answer. '
        'ONLY return the JSON category mapping object.'

    ),
    expected_output=(
        'Return ONLY a valid JSON object in this exact format:\n'
        '{\n'
        '  "level_1": "Category Name",\n'
        '  "level_2": "Subcategory Name",\n'
        '  "level_3": "Sub-subcategory Name",\n'
        '  "level_4": "",\n'
        '  "level_5": "",\n'
        '  "level_6": "",\n'
        '  "level_7": ""\n'
        '}\n'
        'CRITICAL: Use empty strings "" for unused levels. Do NOT include tool calls, explanations, or any other text. '
        'ONLY return the JSON object above with actual category names from search results.'
    ),
    agent=mapper_agent,
    output_pydantic=CategoryMapping
)

# Validation Task
validate_mapping_task = Task(
    description=(
        'Validate the MAPPER AGENT\'S PROPOSED CATEGORY PATH from the previous task result. '
        'DO NOT validate breadcrumbs - validate the mapper agent\'s output only. '
        'Product Info: {product_info}. '

        'The mapper agent\'s result is available from the previous task context.\n\n'

        'CRITICAL VALIDATION RULES: '
        '1. The MAPPER AGENT PROPOSED the category path shown above in MAPPER AGENT RESULT '
        '2. LIMIT search attempts to maximum 2 queries to verify mapper\'s categories '
        '3. Check if the MAPPER\'S proposed categories exist in our database using the search tool '
        '4. If mapper\'s categories do NOT exist in database, status = "rejected" '
        '5. If mapper\'s categories exist and are among the top 3 search results, status = "approved" '
        '6. If mapper\'s categories exist but rank lower than top 3, status = "rejected" '
        '7. If no database categories found after 2 searches, approve mapper result as fallback '
        '8. Use search tool to find the most accurate database category path '
        '9. ONLY approve categories that exist in the database search results '
        '10. IGNORE original breadcrumbs if provided in Product Info - focus only on the MAPPER AGENT RESULT shown above '
        '11. When providing corrected_path, ONLY use exact category paths from search results - DO NOT create new categories '
        '12. corrected_path must be an exact match from the search tool results, not a made-up category '
        '13. If corrected_path would be identical to mapper result, status should be "approved" instead '
        'Provide corrected_path with valid database categories when rejecting.\n\n'

        'TOOL USAGE INSTRUCTIONS:\n'
        'When calling category_hybrid_search, pass ONLY a simple string query:\n\n'

        'CORRECT examples:\n'
        '- category_hybrid_search({"query": "garden tools"})\n'
    ),
    expected_output=(
        'Return ONLY a valid JSON object in this exact format:\n'
        '{\n'
        '  "status": "approved or rejected",\n'
        '  "confidence_score": 0.95,\n'
        '  "corrected_path": null\n'
        '}\n'
    ),
    agent=validation_agent,
    context=[map_product_task],  # This makes the mapper's output available to the validator
    output_pydantic=ValidationResult
)
