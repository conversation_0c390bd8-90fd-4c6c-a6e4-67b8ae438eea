#!/usr/bin/env python
"""
Minimal parallel processing for mapping multiple products.

Focuses solely on processing multiple products in parallel and returning results.
"""

from __future__ import annotations

import concurrent.futures
import os
import time
import threading
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Tuple


@dataclass
class BatchResult:
    """Result from batch processing operation."""
    batch_id: str
    total_items: int
    successful: int
    failed: int
    processing_time_ms: float
    throughput_per_second: float
    errors: List[Dict[str, Any]]
    results: Dict[str, Any]

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'batch_id': self.batch_id,
            'total_items': self.total_items,
            'successful': self.successful,
            'failed': self.failed,
            'processing_time_ms': self.processing_time_ms,
            'throughput_per_second': self.throughput_per_second,
            'success_rate': (self.successful / self.total_items * 100) if self.total_items > 0 else 0,
            'errors': self.errors,
            'results': self.results,
        }

 

class ParallelAgentProcessor:
    """Process products in parallel using a thread pool."""

    def __init__(self, max_workers: Optional[int] = None):
        cpu = os.cpu_count() or 1
        self.max_workers = max_workers or min(cpu, 2)

        # Initialize components that are safe to share across all threads
        from auto_mapping import initialize_search_tool, setup_agents
        self.search_tool = initialize_search_tool()
        self.wrapped_mapper, self.wrapped_validator = setup_agents(self.search_tool)

    def _create_new_crew(self) -> Any:
        """
        Creates a fresh Crew instance for each product.
        This is the safest way to ensure memory isolation in a parallel environment,
        as each crew gets its own memory space, avoiding race conditions.
        """
        from crewai import Crew, Process
        from crew_components.tasks import map_product_task, validate_mapping_task
        verbose=False  # Set to False to prevent hanging in parallel execution

        # By creating a new Crew for each task with memory=True and the default
        # share_crew=False, we ensure each task has a completely isolated
        # memory space. This prevents the "Collection does not exist" error
        # which is a race condition from multiple threads accessing a shared memory store.
        self.wrapped_mapper.verbose = verbose
        self.wrapped_validator.verbose = verbose

        return Crew(
            agents=[self.wrapped_mapper, self.wrapped_validator],
            tasks=[map_product_task, validate_mapping_task],
            memory=True,
            process=Process.sequential,
            verbose=False
        )

    def _process_single_product(self, product: Dict[str, Any]) -> Tuple[str, Dict[str, Any], Optional[str]]:
        product_id = str(product.get("product_id", "unknown"))
        # Create a new crew for each product to ensure memory isolation
        crew = self._create_new_crew()
        try:
            from auto_mapping import process_single_product
            category_path = process_single_product(
                product,
                crew=crew,
                max_retries=2
            )
            if category_path is None:
                return product_id, {}, "Processing failed"
            
           
            return product_id, category_path, None
        except Exception as e:
            return product_id, {}, str(e)

    def process_batch(self, products: List[Dict[str, Any]]) -> BatchResult:
        batch_id = f"batch_{int(time.time())}"
        start = time.time()

        results: Dict[str, Any] = {}
        errors: List[Dict[str, Any]] = []

        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = [executor.submit(self._process_single_product, p) for p in products]
            for future in concurrent.futures.as_completed(futures):
                product_id, result, error = future.result()
                if error:
                    errors.append({"product_id": product_id, "error": error})
                else:
                    results[product_id] = result

        total_time_ms = (time.time() - start) * 1000.0
        successful = len(results)
        failed = len(errors)
        throughput = (len(products) / (total_time_ms / 1000.0)) if total_time_ms > 0 else 0.0

        return BatchResult(
            batch_id=batch_id,
            total_items=len(products),
            successful=successful,
            failed=failed,
            processing_time_ms=total_time_ms,
            throughput_per_second=throughput,
            errors=errors,
            results=results,
        )


class ParallelWorkflowOrchestrator:
    """Minimal orchestrator that only runs agent processing in parallel."""

    def __init__(self, agent_workers: Optional[int] = None):
        self.agent_processor = ParallelAgentProcessor(max_workers=agent_workers)

    def process_products_parallel(
        self,
        products: List[Dict[str, Any]],  
    ) -> Dict[str, Any]:
        start = time.time()
        agent_results_dict: Optional[Dict[str, Any]] = self.agent_processor.process_batch(products).to_dict()
        total_time_ms = (time.time() - start) * 1000.0

        return {
            'orchestration_time_ms': total_time_ms,
            'products_processed': len(products),
            'overall_throughput_per_second': (len(products) / (total_time_ms / 1000.0)) if total_time_ms > 0 else 0.0,
            'faiss_results': None,
            'agent_results': agent_results_dict,
        }
