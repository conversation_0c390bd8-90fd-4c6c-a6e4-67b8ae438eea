# Core CrewAI dependencies
crewai>=0.150.0
crewai-tools>=0.59.0

# LLM and AI dependencies
openai
google-generativeai
langchain
google-cloud-aiplatform>=1.38

# Database dependencies
psycopg2

# Data handling and validation
pydantic>=2.0.0
pydantic-settings>=2.0.0

# Utilities
python-dotenv>=1.0.0
beautifulsoup4>=4.12.0
rich>=13.0.0
tiktoken
faiss-cpu
inflect
pandas
scipy
nltk

# Web framework (if needed)
fastapi
uvicorn

# Development tools
ipykernel