import os
import re
from bs4 import BeautifulSoup
import inflect 
from nltk.tokenize import word_tokenize
from nltk.tag import pos_tag
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class preprocess:
    """
    This class is used to preprocess the data.
    It is used to remove the weight, unwanted sections and other unwanted characters from the data. It also extracts the nouns from the text.
    """

    def __init__(self):
        self.p = inflect.engine()
        logging.info('Initiating preprocess class.')

    def extract_text(self,html_content):
            """
            Extracts and cleans text from HTML content, removing script and style elements.

            :param html_content: HTML content as a string.
            :return: Cleaned text with no HTML tags.
            """
            soup = BeautifulSoup(html_content, 'html.parser')
            for script in soup(["script", "style"]):
                script.decompose()
            text = soup.get_text()
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = '\n'.join(chunk for chunk in chunks if chunk)
            return text
    
    def remove_weight(self,product_name):
        """
        Removes the weight, quantity, packs from the product name and cleans it.

        :param product_name: Product name from which the unwanted text will be removed.
        :return: Text cleaned of unwanted text.
        """
        pattern =  r'\b\d+(\.\d+)?%|\b\d+\s*(g|kg|mg|ml|cm|m|cl|l|L)|\bx\s*\d+|\b\d+\s*x\s*\d+\s*(g|mg|ml|cl|l)?\b'
        cleaned_name = re.sub(pattern, '',product_name).strip()
        pattern = r'\b(kit|set|pack|packs|sets)\b'
        cleaned_name = re.sub(pattern, '', cleaned_name, flags=re.IGNORECASE).strip()
        return cleaned_name
    
    def remove_ingredients(self,text):
        """
        Removes unwanted sections like ingredients and nutritional values from the text.

        :param text: String from which the content will be removed.
        :return: Text cleaned of unwanted sections.
        """
        pattern = (
            r"([A-Za-z ]*[:\s]*)(Ingredients|Nutritional values|Nutrition|Allergens|Allergy Advice|Further nutritional \
                values)[:\s]*.*?(?=(\n\n|$))|"
            r"Energy\s+kJ.*?(?=(\n\n|$))|"
            r"%\s+of the nutrient reference value.*?(?=(\n\n|$))"
        )
        cleaned_text = re.sub(pattern, '', text, flags=re.DOTALL)  # Removed IGNORECASE
        cleaned_text = cleaned_text.replace("\n", " ")
        cleaned_text = cleaned_text.replace("Description Helps remove bacteria For brilliant limescale removal & brilliant shine For a hygienic & deep clean Leaves a barrier that prevents watermarks and limescale build up Used regularly, it will provide clean and shiny surfaces every day The special protection formula prevents limescale from coming back and thus, helps maintain shiny surfaces Viakal Ultra helps remove bacteria leaving your surfaces hygienically clean. Thanks to its thick formula Viakal Ultra sticks longer to vertical surfaces for deep cleaning of soap scum and limescale. Whether you are looking to clean your bathroom sinks or shower or your kitchen stainless steel sinks - the product can be used for all purposes. This multi purpose product is a spray and can be used alongside Flash wipes. Use this product in the bathroom near your toilet/ loo and watch it cleanse your surfaces. You can now kick back on your leather sofa, relax, let off some steam and stare out the window - the cleaning will be done in no time.","")
        cleaned_text = cleaned_text.replace('Description',"")
        return cleaned_text.strip()
    
    def extract_nouns(self,sentence):
        """
        Extracts the nouns from the sentence.

        :param text: String from which the nouns are to be extracted.
        :return: List of nouns from the sentence.
        """
        py_nltkpos = word_tokenize(sentence)
        tagged_sent = pos_tag(py_nltkpos)
        nouns = [word for word,pos in tagged_sent if (pos == 'NN' or pos == "NNP" or pos == 'NNPS' or pos == 'NNS')]
        return list(set(nouns))
    