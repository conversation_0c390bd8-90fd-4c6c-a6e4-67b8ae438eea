FROM python:3.10-slim

# Set the working directory inside the container
WORKDIR /app

RUN apt-get update && apt-get install -y \
    libpq-dev \
    gcc \
    && apt-get clean
RUN python3 -m pip install --upgrade pip

COPY requirements.txt .

# Install any needed packages specified in requirements.txt
RUN pip install --no-cache-dir -r requirements.txt
RUN python -m nltk.downloader punkt_tab averaged_perceptron_tagger_eng

# Copy the current directory contents into the container at /app
COPY . .

EXPOSE 8000

CMD ["uvicorn", "backend.routes:app", "--host", "0.0.0.0", "--port", "8000"]

