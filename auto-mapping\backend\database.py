import os
import pandas as pd
import numpy as np
import faiss
import psycopg2
import psycopg2.extras
import logging
from dotenv import load_dotenv
from psycopg2 import OperationalError, InterfaceError, ProgrammingError, DatabaseError, Error
from typing import Tuple, Optional

load_dotenv()
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


class database:
    """
    Class to connect to the database and make the query to the PostgreSQL tables.
    """
    def __init__(self):
        try:
            self.postgres_config = {
                        "database":os.getenv("POSTGRES_DB"),
                        "host":os.getenv("POSTGRES_HOST"),
                        "user":os.getenv("POSTGRES_USER"),
                        "password":os.getenv("POSTGRES_PASSWORD"),
                        "port":os.getenv("POSTGRES_PORT")}
            self.cnx = psycopg2.connect(**self.postgres_config)
            self.cursor = self.cnx.cursor()
            self.output_table = 'category_output'
            self.index = faiss.read_index(os.getenv('EMBED_PATH'))   
            logging.info("Establishing connection with database.")
        

        except InterfaceError as e:
            raise Exception("Unable to connect to database.")
        
        except ProgrammingError as e:
            if "authentication failed" in str(e):
                raise Exception("Invalid database credentials.")
            else:
                raise Exception(f"Error: {str(e)}")
        
        except DatabaseError as e:
            raise Exception(f"Database operation failed: {str(e)}")
        
        except Exception as e:
            raise Exception(f"An unexpected error occured: {str(e)}")
        
        except Error as e:
            raise Exception(f"An unexpected database error occurred: {str(e)}")
            

    def get_categories(self):
        """
        Query and get the categories table from the database.

        :return: All the categories from the table.
        """
        query = """
        SELECT l1.category_name AS level1_name, 
           l2.category_name AS level2_name, 
           l3.category_name AS level3_name,
           l4.category_name AS level4_name,
           l5.category_name AS level5_name,
           l6.category_name AS level6_name,
           l7.category_name AS level7_name
        FROM categories_level1 l1
        LEFT JOIN categories_level2 l2 ON l1.level1_id = l2.level1_parent
        LEFT JOIN categories_level3 l3 ON l2.level2_id = l3.level2_parent
        LEFT JOIN categories_level4 l4 ON l3.level3_id = l4.level3_parent
        LEFT JOIN categories_level5 l5 ON l4.level4_id = l5.level4_parent
        LEFT JOIN categories_level6 l6 ON l5.level5_id = l6.level5_parent
        LEFT JOIN categories_level7 l7 ON l6.level6_id = l7.level6_parent
        ORDER BY l1.level1_id, l2.level2_id, l3.level3_id, l4.level4_id, l5.level5_id, l6.level6_id, l7.level7_id;
        """
        self.cursor.execute(query)
        result = self.cursor.fetchall()

        return result

    def get_explanation_by_category(self, category_path):
        """
        Query and fetch the explanation of a category.

        :param category_path: The category for which the explanation is needed.
        :return: The explanation of the category from the table.
        """
        category_parts = category_path.split(' > ')
        num_levels = len(category_parts)

        level_names = ['level1', 'level2', 'level3', 'level4', 'level5', 'level6', 'level7']
        conditions = []
        params = []

        # Start building the query with joins
        query = """
        SELECT ce.explanation
        FROM category_explanations ce
        LEFT JOIN categories_level7 l7 ON ce.level7_id = l7.level7_id
        LEFT JOIN categories_level6 l6 ON l7.level6_parent = l6.level6_id OR ce.level6_id = l6.level6_id
        LEFT JOIN categories_level5 l5 ON l6.level5_parent = l5.level5_id OR ce.level5_id = l5.level5_id
        LEFT JOIN categories_level4 l4 ON l5.level4_parent = l4.level4_id OR ce.level4_id = l4.level4_id
        LEFT JOIN categories_level3 l3 ON l4.level3_parent = l3.level3_id OR ce.level3_id = l3.level3_id
        LEFT JOIN categories_level2 l2 ON l3.level2_parent = l2.level2_id OR ce.level2_id = l2.level2_id
        LEFT JOIN categories_level1 l1 ON l2.level1_parent = l1.level1_id OR ce.level1_id = l1.level1_id
        WHERE
        """

        # Build conditions dynamically based on the number of levels provided
        for i in range(num_levels):
            conditions.append(f"l{i+1}.category_name = %s")
            params.append(category_parts[i])

        # For any missing levels, ensure the corresponding IDs are null
        for i in range(num_levels, 7):
            conditions.append(f"ce.{level_names[i]}_id IS NULL")

        # Combine conditions and finish the query
        query += " AND ".join(conditions)
        query += " LIMIT 1"

        # Execute the query
        self.cursor.execute(query, params)
        explanation = self.cursor.fetchone()

        return explanation if explanation else None


    def get_category_ids(self, level_1: str, 
                        level_2: Optional[str] = None, 
                        level_3: Optional[str] = None,
                        level_4: Optional[str] = None,
                        level_5: Optional[str] = None,
                        level_6: Optional[str] = None,
                        level_7: Optional[str] = None) -> Tuple[int, Optional[int], Optional[int], Optional[int], Optional[int], Optional[int], Optional[int]]:
        """
        Get the category ids for a given category path.

        :param level_1: The level1 category name for the category ID should be fetched.
        :param level_2: The level2 category name for the category ID should be fetched.
        :param level_3: The level3 category name for the category ID should be fetched.
        :param level_4: The level4 category name for the category ID should be fetched.
        :param level_5: The level5 category name for the category ID should be fetched.
        :param level_6: The level6 category name for the category ID should be fetched.
        :param level_7: The level7 category name for the category ID should be fetched.
        :return: A tuple containing the level1_id to level7_id
        """
        
        # Fetch level1_id
        self.cursor.execute("SELECT level1_id FROM categories_level1 WHERE category_name = %s", (level_1,))
        level1_id = self.cursor.fetchone()
        if not level1_id:
            raise ValueError("Invalid category levels provided")
        level1_id = level1_id[0]
        
        level2_id = None
        if level_2:
            self.cursor.execute("SELECT level2_id FROM categories_level2 WHERE category_name = %s AND level1_parent = %s", (level_2, level1_id))
            level2_id = self.cursor.fetchone()
            if level2_id:
                level2_id = level2_id[0]
        
        level3_id = None
        if level_3:
            self.cursor.execute("SELECT level3_id FROM categories_level3 WHERE category_name = %s AND level2_parent = %s", (level_3, level2_id))
            level3_id = self.cursor.fetchone()
            if level3_id:
                level3_id = level3_id[0]
        
        level4_id = None
        if level_4:
            self.cursor.execute("SELECT level4_id FROM categories_level4 WHERE category_name = %s AND level3_parent = %s", (level_4, level3_id))
            level4_id = self.cursor.fetchone()
            if level4_id:
                level4_id = level4_id[0]

        level5_id = None
        if level_5:
            self.cursor.execute("SELECT level5_id FROM categories_level5 WHERE category_name = %s AND level4_parent = %s", (level_5, level4_id))
            level5_id = self.cursor.fetchone()
            if level5_id:
                level5_id = level5_id[0]

        level6_id = None
        if level_6:
            self.cursor.execute("SELECT level6_id FROM categories_level6 WHERE category_name = %s AND level5_parent = %s", (level_6, level5_id))
            level6_id = self.cursor.fetchone()
            if level6_id:
                level6_id = level6_id[0]

        level7_id = None
        if level_7:
            self.cursor.execute("SELECT level7_id FROM categories_level7 WHERE category_name = %s AND level6_parent = %s", (level_7, level6_id))
            level7_id = self.cursor.fetchone()
            if level7_id:
                level7_id = level7_id[0]
        
        return level1_id, level2_id, level3_id, level4_id, level5_id, level6_id, level7_id
    
    def check_existing_hard_logic(self, hard_logic_word: str) -> bool:
        """
        Check if a hard logic word already exists in the database.
        :param hard_logic_word: The hard logic word that should be checked if it exists.
        :return: True if the hard logic word already exists, False otherwise.
        """
        self.cursor_dict = self.cnx.cursor(cursor_factory=psycopg2.extras.DictCursor)
        self.cursor_dict.execute("SELECT * FROM new_category_hardlogic WHERE word = %s", (hard_logic_word,))
        result = self.cursor_dict.fetchone()
        self.cursor_dict.close()
        return result
    
    def check_existing_soft_logic(self, soft_logic_word: str) -> bool:
        """
        Check if a soft logic word already exists in the database.
        :param soft_logic_word: The soft logic word that should be checked if it exists.
        :return: True if the soft logic word already exists, False otherwise.
        """
        self.cursor_dict = self.cnx.cursor(cursor_factory=psycopg2.extras.DictCursor)
        self.cursor_dict.execute("SELECT * FROM new_category_kfs WHERE Keyword = %s", (soft_logic_word,))
        result = self.cursor_dict.fetchone()
        self.cursor_dict.close()
        return result
    

    def create_hard_logic(self, word: str, is_pattern: int, level1_id: int, 
                        level2_id: Optional[int] = None, level3_id: Optional[int] = None,
                        level4_id: Optional[int] = None, level5_id: Optional[int] = None,
                        level6_id: Optional[int] = None, level7_id: Optional[int] = None):   
        """
        Create a new hard logic entry in the database.
        
        :param word: The word that should be added as a hard logic entry.
        :param is_pattern: Whether the word is a pattern or not (1 for pattern, 0 for not a pattern).
        :param level1_id: The id of the level 1 category.
        :param level2_id: The id of the level 2 category (optional).
        :param level3_id: The id of the level 3 category (optional).
        :param level4_id: The id of the level 4 category (optional).
        :param level5_id: The id of the level 5 category (optional).
        :param level6_id: The id of the level 6 category (optional).
        :param level7_id: The id of the level 7 category (optional).
        """
        
        # Create the SQL insert query
        query = """
        INSERT INTO new_category_hardlogic (word, is_pattern, level1_id, level2_id, level3_id, level4_id, level5_id, level6_id, level7_id) 
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        # Execute the query with the provided values
        self.cursor.execute(query, (word, is_pattern, level1_id, level2_id, level3_id, level4_id, level5_id, level6_id, level7_id))
        
        # Commit the transaction
        self.cnx.commit()

    def create_soft_logic(self, word: str, level1_id: int, 
                        level2_id: Optional[int] = None, 
                        level3_id: Optional[int] = None,
                        level4_id: Optional[int] = None, 
                        level5_id: Optional[int] = None,
                        level6_id: Optional[int] = None, 
                        level7_id: Optional[int] = None):   
        """
        Create a new soft logic entry in the database.

        :param word: The word that should be added as a soft logic entry.
        :param level1_id: The id of the level 1 category.
        :param level2_id: The id of the level 2 category (optional).
        :param level3_id: The id of the level 3 category (optional).
        :param level4_id: The id of the level 4 category (optional).
        :param level5_id: The id of the level 5 category (optional).
        :param level6_id: The id of the level 6 category (optional).
        :param level7_id: The id of the level 7 category (optional).
        """
        
        # Create the SQL insert query
        query = """
        INSERT INTO new_category_kfs (keyword, level1_id, level2_id, level3_id, level4_id, level5_id, level6_id, level7_id) 
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        # Execute the query with the provided values
        self.cursor.execute(query, (word, level1_id, level2_id, level3_id, level4_id, level5_id, level6_id, level7_id))
        
        # Commit the transaction
        self.cnx.commit()


    
    def update_hard_logic(self, new_hard_logic_string: str, is_pattern: int, 
                        level1_id: Optional[int], level2_id: Optional[int], 
                        level3_id: Optional[int], level4_id: Optional[int],
                        level5_id: Optional[int], level6_id: Optional[int],
                        level7_id: Optional[int], old_hard_logic_string: str):
        """
        Update a hard logic entry in the database.

        :param new_hard_logic_string: The new hard logic that should be updated in place of old_hard_logic_string.
        :param is_pattern: Whether the word is a pattern or not (1 for pattern, 0 for not a pattern).
        :param level1_id: The id of the level 1 category (optional).
        :param level2_id: The id of the level 2 category (optional).
        :param level3_id: The id of the level 3 category (optional).
        :param level4_id: The id of the level 4 category (optional).
        :param level5_id: The id of the level 5 category (optional).
        :param level6_id: The id of the level 6 category (optional).
        :param level7_id: The id of the level 7 category (optional).
        :param old_hard_logic_string: The old hard logic that should be updated.
        """
        
        # Create the SQL update query
        query = """
        UPDATE new_category_hardlogic 
        SET word = %s, is_pattern = %s, level1_id = %s, level2_id = %s, level3_id = %s, 
            level4_id = %s, level5_id = %s, level6_id = %s, level7_id = %s
        WHERE word = %s
        """
        
        # Execute the query with the provided values
        self.cursor.execute(query, (new_hard_logic_string, is_pattern, level1_id, level2_id, level3_id, level4_id, level5_id, level6_id, level7_id, old_hard_logic_string))
        
        # Commit the transaction
        self.cnx.commit()

    def update_soft_logic(self, new_soft_logic_string: str, 
                        level1_id: Optional[int], level2_id: Optional[int], 
                        level3_id: Optional[int], level4_id: Optional[int], 
                        level5_id: Optional[int], level6_id: Optional[int],
                        level7_id: Optional[int], old_soft_logic_string: str):
        """
        Update a soft logic entry in the database.
        
        :param new_soft_logic_string: The new soft logic that should be updated in place of old_soft_logic_string.
        :param level1_id: The id of the level 1 category.
        :param level2_id: The id of the level 2 category (optional).
        :param level3_id: The id of the level 3 category (optional).
        :param level4_id: The id of the level 4 category (optional).
        :param level5_id: The id of the level 5 category (optional).
        :param level6_id: The id of the level 6 category (optional).
        :param level7_id: The id of the level 7 category (optional).
        :param old_soft_logic_string: The old soft logic that should be updated.
        """
        
        # Create the SQL update query
        query = """
        UPDATE new_category_kfs 
        SET keyword = %s, level1_id = %s, level2_id = %s, level3_id = %s, 
            level4_id = %s, level5_id = %s, level6_id = %s, level7_id = %s
        WHERE keyword = %s
        """
        
        # Execute the query with the provided values
        self.cursor.execute(query, (new_soft_logic_string, level1_id, level2_id, level3_id, level4_id, level5_id, level6_id, level7_id, old_soft_logic_string))
        
        # Commit the transaction
        self.cnx.commit()
        

    def delete_hard_logic(self,hardlogic_word: str):
        """
        Delete a hard logic entry from the database.
        :param hardlogic_word: The word that should be deleted from the database.
        """
        self.cursor.execute("DELETE FROM new_category_hardlogic WHERE word = %s", (hardlogic_word,))
        if self.cursor.rowcount == 0:
            raise ValueError("Hard logic rule not found for deletion.")
        self.cnx.commit()

    def delete_soft_logic(self, softlogic_word: str):
        """
        Delete a soft logic entry from the database.
        :param softlogic_word: The word that should be deleted from the database.
        """
        self.cursor.execute("DELETE FROM new_category_kfs WHERE Keyword = %s", (softlogic_word,))
        if self.cursor.rowcount == 0:
            raise ValueError("Soft logic rule not found for deletion.")
        self.cnx.commit()


    def fetch_category_kf(self, main_word):
        """
        Query and fetch the row that matches with the keyword from the KF, including category names for levels 1 to 7.

        :param main_word: The word that should be matched with the keywords from KF.
        :return: A list of category names from levels 1 to 7 that match with the keyword.
        """

        # Query to fetch category names for all levels in one go using LEFT JOIN
        query = """
        SELECT l1.category_name AS level1_name, 
            l2.category_name AS level2_name, 
            l3.category_name AS level3_name, 
            l4.category_name AS level4_name, 
            l5.category_name AS level5_name, 
            l6.category_name AS level6_name, 
            l7.category_name AS level7_name
        FROM new_category_kfs kfs
        LEFT JOIN categories_level1 l1 ON kfs.level1_id = l1.level1_id
        LEFT JOIN categories_level2 l2 ON kfs.level2_id = l2.level2_id
        LEFT JOIN categories_level3 l3 ON kfs.level3_id = l3.level3_id
        LEFT JOIN categories_level4 l4 ON kfs.level4_id = l4.level4_id
        LEFT JOIN categories_level5 l5 ON kfs.level5_id = l5.level5_id
        LEFT JOIN categories_level6 l6 ON kfs.level6_id = l6.level6_id
        LEFT JOIN categories_level7 l7 ON kfs.level7_id = l7.level7_id
        WHERE kfs.keyword = %s
        """

        # Execute the query
        self.cursor.execute(query, (main_word,))
        result = self.cursor.fetchone()

        if result is None:
            return None

        # Return the list of category names for levels 1 to 7
        return list(result)
    
    def fetch_kf_keywords(self):
        """
        Query and fetch the keywords from the KF table.

        :return: List of all words from the column Keyword from KF.
        """
        query = "SELECT Keyword FROM new_category_kfs"
        self.cursor.execute(query)
        result = [row[0].lower() for row in self.cursor.fetchall()]
        return result
    
    def fetch_patterns(self):
        """
        Fetches all patterns from the category_hardlogic table.

        :return: List of tuples containing the word patterns.
        """
        query_pattern = "SELECT word FROM new_category_hardlogic WHERE is_pattern = 1"
        self.cursor.execute(query_pattern)
        result = self.cursor.fetchall()
        return result
    
    def fetch_hardlogic_words(self):
        """
        Fetches all words from the category_hardlogic table.

        :return: List of words from the word column of category_hardlogic table.
        """
        query_pattern = "SELECT word FROM new_category_hardlogic WHERE is_pattern = 0"
        self.cursor.execute(query_pattern)
        result = self.cursor.fetchall()
        return result
    
    def fetch_category_hardlogic(self, main_word):
        """
        Fetches the row that matches with the main_word from the category_hardlogic table.

        :param main_word: The word that needs to be checked and matched with the hard logics.
        :return: A list of category names from levels 1 to 7 that match with the main_word.
        """

        # Query to fetch category names for all levels in one go using LEFT JOIN
        query = """
        SELECT l1.category_name AS level1_name, 
            l2.category_name AS level2_name, 
            l3.category_name AS level3_name, 
            l4.category_name AS level4_name, 
            l5.category_name AS level5_name, 
            l6.category_name AS level6_name, 
            l7.category_name AS level7_name
        FROM new_category_hardlogic hl
        LEFT JOIN categories_level1 l1 ON hl.level1_id = l1.level1_id
        LEFT JOIN categories_level2 l2 ON hl.level2_id = l2.level2_id
        LEFT JOIN categories_level3 l3 ON hl.level3_id = l3.level3_id
        LEFT JOIN categories_level4 l4 ON hl.level4_id = l4.level4_id
        LEFT JOIN categories_level5 l5 ON hl.level5_id = l5.level5_id
        LEFT JOIN categories_level6 l6 ON hl.level6_id = l6.level6_id
        LEFT JOIN categories_level7 l7 ON hl.level7_id = l7.level7_id
        WHERE hl.word = %s
        """

        # Execute the query
        self.cursor.execute(query, (main_word,))
        result = self.cursor.fetchone()

        if result is None:
            return None

        # Return the list of category names for levels 1 to 7
        return list(result)

    def get_category_hardlogic_word(self, main_word):
        """
        Fetches the row that matches with the main_word from the new_category_hardlogic table, including is_pattern and category names for levels 1 to 7.

        :param main_word: The word that needs to be checked and matched with the hard logics.
        :return: A list containing the is_pattern value and category names for levels 1 to 7.
        """
        
        # Query to fetch the is_pattern value and category names for all levels in one go using LEFT JOIN
        query = """
        SELECT hl.is_pattern, 
            l1.category_name AS level1_name, 
            l2.category_name AS level2_name, 
            l3.category_name AS level3_name, 
            l4.category_name AS level4_name, 
            l5.category_name AS level5_name, 
            l6.category_name AS level6_name, 
            l7.category_name AS level7_name
        FROM new_category_hardlogic hl
        LEFT JOIN categories_level1 l1 ON hl.level1_id = l1.level1_id
        LEFT JOIN categories_level2 l2 ON hl.level2_id = l2.level2_id
        LEFT JOIN categories_level3 l3 ON hl.level3_id = l3.level3_id
        LEFT JOIN categories_level4 l4 ON hl.level4_id = l4.level4_id
        LEFT JOIN categories_level5 l5 ON hl.level5_id = l5.level5_id
        LEFT JOIN categories_level6 l6 ON hl.level6_id = l6.level6_id
        LEFT JOIN categories_level7 l7 ON hl.level7_id = l7.level7_id
        WHERE hl.word = %s
        """
        
        # Execute the query
        self.cursor.execute(query, (main_word,))
        result = self.cursor.fetchone()

        if result is None:
            return None

        # Return the list of is_pattern and category names for levels 1 to 7
        return list(result)

    def get_rows_categories(self, level):
        """
        Fetches the rows from the category table that are not null at the specified level.
        
        :param level: The level that needs to be checked from the category table (Level 1 to Level 7).
        :return: The rows that match with the condition from the category table.
        """
        # Mapping of levels to their aliases and columns
        level_map = {
            'Level 1': 'l1.category_name IS NOT NULL',
            'Level 2': 'l2.category_name IS NOT NULL',
            'Level 3': 'l3.category_name IS NOT NULL',
            'Level 4': 'l4.category_name IS NOT NULL',
            'Level 5': 'l5.category_name IS NOT NULL',
            'Level 6': 'l6.category_name IS NOT NULL',
            'Level 7': 'l7.category_name IS NOT NULL'
        }
        
        # Base query structure
        base_query = """
            SELECT l1.category_name AS "Level 1", l2.category_name AS "Level 2", l3.category_name AS "Level 3",
                l4.category_name AS "Level 4", l5.category_name AS "Level 5", l6.category_name AS "Level 6",
                l7.category_name AS "Level 7"
            FROM categories_level1 l1
            LEFT JOIN categories_level2 l2 ON l1.level1_id = l2.level1_parent
            LEFT JOIN categories_level3 l3 ON l2.level2_id = l3.level2_parent
            LEFT JOIN categories_level4 l4 ON l3.level3_id = l4.level3_parent
            LEFT JOIN categories_level5 l5 ON l4.level4_id = l5.level4_parent
            LEFT JOIN categories_level6 l6 ON l5.level5_id = l6.level5_parent
            LEFT JOIN categories_level7 l7 ON l6.level6_id = l7.level6_parent
            WHERE {}
        """
        
        # Construct the full query based on the specified level
        where_clause = level_map.get(level)
        query = base_query.format(where_clause)
        
        # Execute the query and fetch results
        self.cursor.execute(query)
        rows_to_check = self.cursor.fetchall()
        
        return rows_to_check



    def get_level_ids(self, category_path):
        """
        Fetch the level IDs from level1 to level7 based on the given category path.

        :param category_path: The category path in the format 'Level 1 > Level 2 > ... > Level 7'
        :return: A tuple containing the level1_id, level2_id, level3_id, level4_id, level5_id, level6_id, level7_id
        """
        category_parts = category_path.split(" > ")
        level1_name = category_parts[0] if len(category_parts) > 0 else None
        level2_name = category_parts[1] if len(category_parts) > 1 else None
        level3_name = category_parts[2] if len(category_parts) > 2 else None
        level4_name = category_parts[3] if len(category_parts) > 3 else None
        level5_name = category_parts[4] if len(category_parts) > 4 else None
        level6_name = category_parts[5] if len(category_parts) > 5 else None
        level7_name = category_parts[6] if len(category_parts) > 6 else None

        if level7_name:
            query = """
                SELECT level7_id FROM categories_level7 
                WHERE category_name = %s AND level6_parent = (
                    SELECT level6_id FROM categories_level6 
                    WHERE category_name = %s AND level5_parent = (
                        SELECT level5_id FROM categories_level5 
                        WHERE category_name = %s AND level4_parent = (
                            SELECT level4_id FROM categories_level4 
                            WHERE category_name = %s AND level3_parent = (
                                SELECT level3_id FROM categories_level3 
                                WHERE category_name = %s AND level2_parent = (
                                    SELECT level2_id FROM categories_level2 
                                    WHERE category_name = %s AND level1_parent = (
                                        SELECT level1_id FROM categories_level1 
                                        WHERE category_name = %s
                                    )
                                )
                            )
                        )
                    )
                )
            """
            self.cursor.execute(query, (level7_name, level6_name, level5_name, level4_name, level3_name, level2_name, level1_name))
            result = self.cursor.fetchone()
            if result:
                return None, None, None, None, None, None, result[0]

        elif level6_name:
            query = """
                SELECT level6_id FROM categories_level6 
                WHERE category_name = %s AND level5_parent = (
                    SELECT level5_id FROM categories_level5 
                    WHERE category_name = %s AND level4_parent = (
                        SELECT level4_id FROM categories_level4 
                        WHERE category_name = %s AND level3_parent = (
                            SELECT level3_id FROM categories_level3 
                            WHERE category_name = %s AND level2_parent = (
                                SELECT level2_id FROM categories_level2 
                                WHERE category_name = %s AND level1_parent = (
                                    SELECT level1_id FROM categories_level1 
                                    WHERE category_name = %s
                                )
                            )
                        )
                    )
                )
            """
            self.cursor.execute(query, (level6_name, level5_name, level4_name, level3_name, level2_name, level1_name))
            result = self.cursor.fetchone()
            if result:
                return None, None, None, None, None, result[0], None

        elif level5_name:
            query = """
                SELECT level5_id FROM categories_level5 
                WHERE category_name = %s AND level4_parent = (
                    SELECT level4_id FROM categories_level4 
                    WHERE category_name = %s AND level3_parent = (
                        SELECT level3_id FROM categories_level3 
                        WHERE category_name = %s AND level2_parent = (
                            SELECT level2_id FROM categories_level2 
                            WHERE category_name = %s AND level1_parent = (
                                SELECT level1_id FROM categories_level1 
                                WHERE category_name = %s
                            )
                        )
                    )
                )
            """
            self.cursor.execute(query, (level5_name, level4_name, level3_name, level2_name, level1_name))
            result = self.cursor.fetchone()
            if result:
                return None, None, None, None, result[0], None, None

        elif level4_name:
            query = """
                SELECT level4_id FROM categories_level4 
                WHERE category_name = %s AND level3_parent = (
                    SELECT level3_id FROM categories_level3 
                    WHERE category_name = %s AND level2_parent = (
                        SELECT level2_id FROM categories_level2 
                        WHERE category_name = %s AND level1_parent = (
                            SELECT level1_id FROM categories_level1 
                            WHERE category_name = %s
                        )
                    )
                )
            """
            self.cursor.execute(query, (level4_name, level3_name, level2_name, level1_name))
            result = self.cursor.fetchone()
            if result:
                return None, None, None, result[0], None, None, None

        elif level3_name:
            query = """
                SELECT level3_id FROM categories_level3 
                WHERE category_name = %s AND level2_parent = (
                    SELECT level2_id FROM categories_level2 
                    WHERE category_name = %s AND level1_parent = (
                        SELECT level1_id FROM categories_level1 
                        WHERE category_name = %s
                    )
                )
            """
            self.cursor.execute(query, (level3_name, level2_name, level1_name))
            result = self.cursor.fetchone()
            if result:
                return None, None, result[0], None, None, None, None

        elif level2_name:
            query = """
                SELECT level2_id FROM categories_level2 
                WHERE category_name = %s AND level1_parent = (
                    SELECT level1_id FROM categories_level1 
                    WHERE category_name = %s
                )
            """
            self.cursor.execute(query, (level2_name, level1_name))
            result = self.cursor.fetchone()
            if result:
                return None, result[0], None, None, None, None, None

        elif level1_name:
            query = "SELECT level1_id FROM categories_level1 WHERE category_name = %s"
            self.cursor.execute(query, (level1_name,))
            result = self.cursor.fetchone()
            if result:
                return result[0], None, None, None, None, None, None

        return None, None, None, None, None, None, None


    def get_explanation_id(self, level1_id, level2_id, level3_id, level4_id, level5_id, level6_id, level7_id):
        """
        Fetches the explanation ID based on the category levels (from level1 to level7).

        :param level1_id: The ID of the level 1 category.
        :param level2_id: The ID of the level 2 category.
        :param level3_id: The ID of the level 3 category.
        :param level4_id: The ID of the level 4 category (optional).
        :param level5_id: The ID of the level 5 category (optional).
        :param level6_id: The ID of the level 6 category (optional).
        :param level7_id: The ID of the level 7 category (optional).
        :return: The explanation ID if found, otherwise None.
        """
        
        query = """
            SELECT id FROM category_explanations 
            WHERE (level1_id = %s OR level1_id IS NULL) AND 
                (level2_id = %s OR level2_id IS NULL) AND 
                (level3_id = %s OR level3_id IS NULL) AND
                (level4_id = %s OR level4_id IS NULL) AND
                (level5_id = %s OR level5_id IS NULL) AND
                (level6_id = %s OR level6_id IS NULL) AND
                (level7_id = %s OR level7_id IS NULL)
        """
        
        self.cursor.execute(query, (level1_id, level2_id, level3_id, level4_id, level5_id, level6_id, level7_id))
        result = self.cursor.fetchone()
        
        if result:
            return result[0]
        
        return None

    def get_category_list(self, level_curr, level_next, category_path):
        """
        Fetches the category list of the next level from the category table that are not null.

        :param level_curr: The current level of the category.
        :param level_next: The next level of the category.
        :param category_path: The category path till the current level.
        :return: List of categories that are available for the next level.
        """
        level_map = {
            'Level 1': ('categories_level1', 'category_name', 'level1_id', 'level2_child'),
            'Level 2': ('categories_level2', 'category_name', 'level2_id', 'level3_child', 'level1_parent'),
            'Level 3': ('categories_level3', 'category_name', 'level3_id', 'level4_child', 'level2_parent'),
            'Level 4': ('categories_level4', 'category_name', 'level4_id', 'level5_child', 'level3_parent'),
            'Level 5': ('categories_level5', 'category_name', 'level5_id', 'level6_child', 'level4_parent'),
            'Level 6': ('categories_level6', 'category_name', 'level6_id', 'level7_child', 'level5_parent'),
            'Level 7': ('categories_level7', 'category_name', 'level7_id', None, 'level6_parent')
        }

        # Extract relevant info for the current and next levels from the level_map
        curr_table, curr_name, curr_id, curr_child = level_map[level_curr][:4]
        next_table, next_name, next_id, next_child = level_map[level_next][:4]

        # Split the category path based on the " > " delimiter
        category_split = category_path.split(" > ")

        # Initialize join condition and where clauses
        join_condition = ''
        where_clauses = []
        params = []

        # Handling dynamic joins and where clauses for different levels
        if level_curr == 'Level 1' and level_next == 'Level 2':
            join_condition = f"{next_table}.level1_parent = {curr_table}.{curr_id}"
            where_clauses.append(f"{curr_table}.{curr_name} = %s")
            params.append(category_split[0])

        elif level_curr == 'Level 2' and level_next == 'Level 3':
            join_condition = f"{next_table}.level2_parent = {curr_table}.{curr_id}"
            where_clauses.append(f"{curr_table}.{curr_name} = %s")
            where_clauses.append(f"{curr_table}.level1_parent = (SELECT {level_map['Level 1'][2]} FROM {level_map['Level 1'][0]} WHERE {level_map['Level 1'][1]} = %s)")
            params.extend([category_split[1], category_split[0]])

        elif level_curr == 'Level 3' and level_next == 'Level 4':
            join_condition = f"{next_table}.level3_parent = {curr_table}.{curr_id}"
            where_clauses.append(f"{curr_table}.{curr_name} = %s")
            where_clauses.append(f"{curr_table}.level2_parent = (SELECT {level_map['Level 2'][2]} FROM {level_map['Level 2'][0]} WHERE {level_map['Level 2'][1]} = %s AND level1_parent = (SELECT {level_map['Level 1'][2]} FROM {level_map['Level 1'][0]} WHERE {level_map['Level 1'][1]} = %s))")
            params.extend([category_split[2], category_split[1], category_split[0]])

        elif level_curr == 'Level 4' and level_next == 'Level 5':
            join_condition = f"{next_table}.level4_parent = {curr_table}.{curr_id}"
            where_clauses.append(f"{curr_table}.{curr_name} = %s")
            where_clauses.append(f"{curr_table}.level3_parent = (SELECT {level_map['Level 3'][2]} FROM {level_map['Level 3'][0]} WHERE {level_map['Level 3'][1]} = %s AND level2_parent = (SELECT {level_map['Level 2'][2]} FROM {level_map['Level 2'][0]} WHERE {level_map['Level 2'][1]} = %s AND level1_parent = (SELECT {level_map['Level 1'][2]} FROM {level_map['Level 1'][0]} WHERE {level_map['Level 1'][1]} = %s)))")
            params.extend([category_split[3], category_split[2], category_split[1], category_split[0]])

        elif level_curr == 'Level 5' and level_next == 'Level 6':
            join_condition = f"{next_table}.level5_parent = {curr_table}.{curr_id}"
            where_clauses.append(f"{curr_table}.{curr_name} = %s")
            where_clauses.append(f"{curr_table}.level4_parent = (SELECT {level_map['Level 4'][2]} FROM {level_map['Level 4'][0]} WHERE {level_map['Level 4'][1]} = %s AND level3_parent = (SELECT {level_map['Level 3'][2]} FROM {level_map['Level 3'][0]} WHERE {level_map['Level 3'][1]} = %s AND level2_parent = (SELECT {level_map['Level 2'][2]} FROM {level_map['Level 2'][0]} WHERE {level_map['Level 2'][1]} = %s AND level1_parent = (SELECT {level_map['Level 1'][2]} FROM {level_map['Level 1'][0]} WHERE {level_map['Level 1'][1]} = %s))))")
            params.extend([category_split[4], category_split[3], category_split[2], category_split[1], category_split[0]])

        elif level_curr == 'Level 6' and level_next == 'Level 7':
            join_condition = f"{next_table}.level6_parent = {curr_table}.{curr_id}"
            where_clauses.append(f"{curr_table}.{curr_name} = %s")
            where_clauses.append(f"{curr_table}.level5_parent = (SELECT {level_map['Level 5'][2]} FROM {level_map['Level 5'][0]} WHERE {level_map['Level 5'][1]} = %s AND level4_parent = (SELECT {level_map['Level 4'][2]} FROM {level_map['Level 4'][0]} WHERE {level_map['Level 4'][1]} = %s AND level3_parent = (SELECT {level_map['Level 3'][2]} FROM {level_map['Level 3'][0]} WHERE {level_map['Level 3'][1]} = %s AND level2_parent = (SELECT {level_map['Level 2'][2]} FROM {level_map['Level 2'][0]} WHERE {level_map['Level 2'][1]} = %s AND level1_parent = (SELECT {level_map['Level 1'][2]} FROM {level_map['Level 1'][0]} WHERE {level_map['Level 1'][1]} = %s)))))")
            params.extend([category_split[5], category_split[4], category_split[3], category_split[2], category_split[1], category_split[0]])

        query = f"""
        SELECT DISTINCT {next_table}.{next_name} 
        FROM {curr_table}
        JOIN {next_table} ON {join_condition}
        WHERE {' AND '.join(where_clauses)}
        """

        self.cursor.execute(query, tuple(params))
        category_list = [item[0] for item in self.cursor.fetchall() if item[0] is not None]
        return category_list



    def get_explanation_embed(self,category_path):
        """
        Fetches the explanation embed of the category from the faiss index file.
        :param category_path: The category path for which the explanations embed are to be fetched.
        :return: The explanation embed of the category.
        """
        level1_id, level2_id, level3_id, level4_id, level5_id, level6_id, level7_id = self.get_level_ids(category_path)
        explanation_id = self.get_explanation_id(level1_id, level2_id, level3_id, level4_id, level5_id, level6_id, level7_id)
        return list(self.index.reconstruct(explanation_id))
    
    
    def get_category_bylevel(self, level):
        """
        Fetches the category list of that level from the categories table that are not null at that level.

        :param level: The level for which the categories are to be fetched (Level 1 to Level 7).
        :return: List of categories that are available for that level.
        """
        # Mapping of levels to their respective tables
        level_table_mapping = {
            'Level 1': 'categories_level1',
            'Level 2': 'categories_level2',
            'Level 3': 'categories_level3',
            'Level 4': 'categories_level4',
            'Level 5': 'categories_level5',
            'Level 6': 'categories_level6',
            'Level 7': 'categories_level7'
        }

        # Get the table name corresponding to the given level
        table_name = level_table_mapping.get(level)

        if not table_name:
            raise ValueError(f"Invalid level: {level}. Level must be between 'Level 1' and 'Level 7'.")

        # Query to fetch distinct category names from the selected table
        query = f"SELECT DISTINCT category_name FROM {table_name}"
        
        self.cursor.execute(query)
        categories = [item[0] for item in self.cursor.fetchall() if item[0] is not None]

        return categories


    def get_categories_dict(self):
        """
        Query and get the categories from level 1 to level 7 from the database.

        :return: All the categories from the table.
        """
        self.cursor_dict = self.cnx.cursor(cursor_factory=psycopg2.extras.DictCursor)

        query = """
        SELECT 
            l1.level1_id AS level1_id,
            l1.category_name AS level1_name,
            l2.level2_id AS level2_id,
            l2.category_name AS level2_name,
            l3.level3_id AS level3_id,
            l3.category_name AS level3_name,
            l4.level4_id AS level4_id,
            l4.category_name AS level4_name,
            l5.level5_id AS level5_id,
            l5.category_name AS level5_name,
            l6.level6_id AS level6_id,
            l6.category_name AS level6_name,
            l7.level7_id AS level7_id,
            l7.category_name AS level7_name
        FROM 
            categories_level1 l1
        LEFT JOIN 
            categories_level2 l2 ON l1.level1_id = l2.level1_parent
        LEFT JOIN 
            categories_level3 l3 ON l2.level2_id = l3.level2_parent
        LEFT JOIN 
            categories_level4 l4 ON l3.level3_id = l4.level3_parent
        LEFT JOIN 
            categories_level5 l5 ON l4.level4_id = l5.level4_parent
        LEFT JOIN 
            categories_level6 l6 ON l5.level5_id = l6.level5_parent
        LEFT JOIN 
            categories_level7 l7 ON l6.level6_id = l7.level6_parent
        ORDER BY l1.level1_id, l2.level2_id, l3.level3_id, l4.level4_id, l5.level5_id, l6.level6_id, l7.level7_id;
        """
        
        # Execute the query and fetch all results
        self.cursor_dict.execute(query)
        result = self.cursor_dict.fetchall()
        
        # Close the cursor
        self.cursor_dict.close()

        return result


    def get_categoryname_info(self, category_name):
        """
        Fetches the category name and its corresponding level from the categories table.
        
        :param category_name: The category name for which the parent information is required.
        :return: The category name and its corresponding parent levels.
        """
        self.cursor_dict = self.cnx.cursor(cursor_factory=psycopg2.extras.DictCursor)
        query = """
        SELECT
            'level1' AS level,
            l1.level1_id::TEXT AS id,
            l1.category_name,
            NULL AS level1_parent_name,
            NULL AS level2_parent_name,
            NULL AS level3_parent_name,
            NULL AS level4_parent_name,
            NULL AS level5_parent_name,
            NULL AS level6_parent_name,
            NULL AS level7_parent_name
        FROM categories_level1 l1
        WHERE l1.category_name = %s

        UNION ALL

        SELECT
            'level2' AS level,
            l2.level2_id::TEXT AS id,
            l2.category_name,
            l1.category_name AS level1_parent_name,
            NULL AS level2_parent_name,
            NULL AS level3_parent_name,
            NULL AS level4_parent_name,
            NULL AS level5_parent_name,
            NULL AS level6_parent_name,
            NULL AS level7_parent_name
        FROM categories_level2 l2
        JOIN categories_level1 l1 ON l1.level1_id = l2.level1_parent
        WHERE l2.category_name = %s

        UNION ALL

        SELECT
            'level3' AS level,
            l3.level3_id::TEXT AS id,
            l3.category_name,
            l1.category_name AS level1_parent_name,
            l2.category_name AS level2_parent_name,
            NULL AS level3_parent_name,
            NULL AS level4_parent_name,
            NULL AS level5_parent_name,
            NULL AS level6_parent_name,
            NULL AS level7_parent_name
        FROM categories_level3 l3
        JOIN categories_level2 l2 ON l2.level2_id = l3.level2_parent
        JOIN categories_level1 l1 ON l1.level1_id = l2.level1_parent
        WHERE l3.category_name = %s

        UNION ALL

        SELECT
            'level4' AS level,
            l4.level4_id::TEXT AS id,
            l4.category_name,
            l1.category_name AS level1_parent_name,
            l2.category_name AS level2_parent_name,
            l3.category_name AS level3_parent_name,
            NULL AS level4_parent_name,
            NULL AS level5_parent_name,
            NULL AS level6_parent_name,
            NULL AS level7_parent_name
        FROM categories_level4 l4
        JOIN categories_level3 l3 ON l3.level3_id = l4.level3_parent
        JOIN categories_level2 l2 ON l2.level2_id = l3.level2_parent
        JOIN categories_level1 l1 ON l1.level1_id = l2.level1_parent
        WHERE l4.category_name = %s

        UNION ALL

        SELECT
            'level5' AS level,
            l5.level5_id::TEXT AS id,
            l5.category_name,
            l1.category_name AS level1_parent_name,
            l2.category_name AS level2_parent_name,
            l3.category_name AS level3_parent_name,
            l4.category_name AS level4_parent_name,
            NULL AS level5_parent_name,
            NULL AS level6_parent_name,
            NULL AS level7_parent_name
        FROM categories_level5 l5
        JOIN categories_level4 l4 ON l4.level4_id = l5.level4_parent
        JOIN categories_level3 l3 ON l3.level3_id = l4.level3_parent
        JOIN categories_level2 l2 ON l2.level2_id = l3.level2_parent
        JOIN categories_level1 l1 ON l1.level1_id = l2.level1_parent
        WHERE l5.category_name = %s

        UNION ALL

        SELECT
            'level6' AS level,
            l6.level6_id::TEXT AS id,
            l6.category_name,
            l1.category_name AS level1_parent_name,
            l2.category_name AS level2_parent_name,
            l3.category_name AS level3_parent_name,
            l4.category_name AS level4_parent_name,
            l5.category_name AS level5_parent_name,
            NULL AS level6_parent_name,
            NULL AS level7_parent_name
        FROM categories_level6 l6
        JOIN categories_level5 l5 ON l5.level5_id = l6.level5_parent
        JOIN categories_level4 l4 ON l4.level4_id = l5.level4_parent
        JOIN categories_level3 l3 ON l3.level3_id = l4.level3_parent
        JOIN categories_level2 l2 ON l2.level2_id = l3.level2_parent
        JOIN categories_level1 l1 ON l1.level1_id = l2.level1_parent
        WHERE l6.category_name = %s

        UNION ALL

        SELECT
            'level7' AS level,
            l7.level7_id::TEXT AS id,
            l7.category_name,
            l1.category_name AS level1_parent_name,
            l2.category_name AS level2_parent_name,
            l3.category_name AS level3_parent_name,
            l4.category_name AS level4_parent_name,
            l5.category_name AS level5_parent_name,
            l6.category_name AS level6_parent_name,
            NULL AS level7_parent_name
        FROM categories_level7 l7
        JOIN categories_level6 l6 ON l6.level6_id = l7.level6_parent
        JOIN categories_level5 l5 ON l5.level5_id = l6.level5_parent
        JOIN categories_level4 l4 ON l4.level4_id = l5.level4_parent
        JOIN categories_level3 l3 ON l3.level3_id = l4.level3_parent
        JOIN categories_level2 l2 ON l2.level2_id = l3.level2_parent
        JOIN categories_level1 l1 ON l1.level1_id = l2.level1_parent
        WHERE l7.category_name = %s;
        """
        
        self.cursor_dict.execute(query, (category_name, category_name, category_name, category_name, category_name, category_name, category_name))
        results = self.cursor_dict.fetchall()
        self.cursor_dict.close()
        return results


    def update_table_from_df(self, product_id, output_df):
        """Update the output table from the dataframe

        :param product_id: The product ID for which the table needs to be updated.
        :param output_df: The dataframe containing the updated data.        
        """
        # Replace NaN values with None for the database
        output_df = output_df.where(pd.notnull(output_df), None)

        # Check if the product already exists in the table
        query = (
            f"SELECT COUNT(*) FROM {self.output_table} WHERE product_id = %s"
        )
        self.cursor.execute(query, (product_id,))
        result = self.cursor.fetchone()[0]

        if result > 0:
            # Update the existing row
            update_query = (
                f"UPDATE {self.output_table} SET "
                "product_embed_cost = %s, "
                "hard_logic_word = %s, "
                "category_path_hardlogic = %s, "
                "keywords_nouns = %s, "
                "soft_logic_words = %s, "
                "main_item = %s, "
                "category_path_keyword = %s, "
                "main_item_cost = %s, "
                "embed_cost_nouns = %s, "
                "cost_mainitem_category = %s, "
                "options1 = %s, "
                "llm_output1 = %s, "
                "cost1 = %s, "
                "options2 = %s, "
                "llm_output2 = %s, "
                "cost2 = %s, "
                "options3 = %s, "
                "llm_output3 = %s, "
                "cost3 = %s, "
                "options4 = %s, "
                "llm_output4 = %s, "
                "cost4 = %s, "
                "options5 = %s, "
                "llm_output5 = %s, "
                "cost5 = %s, "
                "options6 = %s, "
                "llm_output6 = %s, "
                "cost6 = %s, "
                "options7 = %s, "
                "llm_output7 = %s, "
                "cost7 = %s, "
                "is_hallucination = %s, "
                "level_1 = %s, "
                "level_2 = %s, "
                "level_3 = %s, "
                "level_4 = %s, "
                "level_5 = %s, "
                "level_6 = %s, "
                "level_7 = %s "
                "WHERE product_id = %s"
            )

            # Prepare the data to be updated
            update_data = tuple(output_df.iloc[0][3:]) + (product_id,)
            update_data = [int(value) if isinstance(value, np.int64) else value for value in update_data]
            self.cursor.execute(update_query, update_data)
        
        else:
            # Insert a new row if it doesn't exist
            insert_query = (
                f"INSERT INTO {self.output_table} "
                "(product_id, product_name, product_description, product_embed_cost, hard_logic_word, category_path_hardlogic, "
                "keywords_nouns, soft_logic_words, main_item, category_path_keyword, main_item_cost, "
                "embed_cost_nouns, cost_mainitem_category, options1, llm_output1, cost1, "
                "options2, llm_output2, cost2, options3, llm_output3, cost3, "
                "options4, llm_output4, cost4, options5, llm_output5, cost5, "
                "options6, llm_output6, cost6, options7, llm_output7, cost7, "
                "is_hallucination, level_1, level_2, level_3, level_4, level_5, level_6, level_7) "
                "VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, "
                "%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, "
                "%s, %s, %s, %s, %s, %s, %s, %s)"
            )

            # Prepare the data to be inserted
            insert_data = tuple(output_df.iloc[0])
            insert_data = [int(value) if isinstance(value, np.int64) else value for value in insert_data]
            self.cursor.execute(insert_query, insert_data)

        # Commit the changes to the database
        self.cnx.commit()
        logging.info("Updated the output table with the product information.\n")


    def close_connection(self):
        """Close the database connection."""
        self.cursor.close()
        self.cnx.close()
        logging.info("Disconnecting the database connection and exiting the category_generator class.")

