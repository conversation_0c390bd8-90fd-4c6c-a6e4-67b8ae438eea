import os
import json
from fastapi import FastAPI, HTTPException, Request, status
from pydantic import BaseModel, Field
from fastapi.responses import JSONResponse
from typing import List, Dict, Optional, Any
from backend.database import database
from backend.category_generator import category_generator
import concurrent.futures
import logging
import traceback
logger = logging.getLogger("uvicorn")
logger.setLevel(logging.DEBUG)

app = FastAPI()

class Product(BaseModel):
    product_id: int
    title: str
    description: str

class CategoryResponse(BaseModel):
    product_id: int
    level_1: Optional[str]
    level_2: Optional[str]
    level_3: Optional[str]
    level_4: Optional[str]  # Added for level 4
    level_5: Optional[str]  # Added for level 5
    level_6: Optional[str]  # Added for level 6
    level_7: Optional[str]  # Added for level 7

class ResponseModel(BaseModel):
    status: str
    message: str
    data: Optional[Any]
    
@app.post("/get_product_mapping", response_model=ResponseModel)
def get_product_mapping(products: List[Product]):
    """
    This endpoint accepts a list of products and returns a list of product mappings with their corresponding categories.
    :param products: List of products for categorization.
    """
    response_data = []
    map = category_generator()
    
    try:
        for product in products:
            category = map.generator(product.product_id, product.title, product.description)
            if category:
                response_data.append(CategoryResponse(
                    product_id=product.product_id,
                    level_1=category.get('Level 1'),
                    level_2=category.get('Level 2'),
                    level_3=category.get('Level 3'),
                    level_4=category.get('Level 4'),
                    level_5=category.get('Level 5'),
                    level_6=category.get('Level 6'),
                    level_7=category.get('Level 7') 
                ))
            else:
                response_data.append(CategoryResponse(
                    product_id = product.product_id,
                    level_1=None,
                    level_2=None,
                    level_3=None,
                    level_4=None,  
                    level_5=None,  
                    level_6=None,  
                    level_7=None  
                ))

        map.exit_generator()
        return ResponseModel(
            status="Success",
            message="Successfully mapped categories.",
            data=response_data
        )
    
    except ValueError as e:
        raise HTTPException(status_code=400, detail={"status": "Error", "message": str(e), "data": {}})
    except Exception as e:
        raise HTTPException(status_code=500, detail={"status": "Error", "message": str(e), "data": {}})
    
def process_product(product: Product, map) -> CategoryResponse:
    """
    This function processes a product and returns its corresponding category.
    :param product: Product to be categorized.
    :param map: Category generator class.
    """
    category = map.generator(product.product_id, product.title, product.description)
    if category:
        return CategoryResponse(
            product_id=product.product_id,
            level_1=category.get('Level 1'),
            level_2=category.get('Level 2'),
            level_3=category.get('Level 3'),
            level_4=category.get('Level 4'), 
            level_5=category.get('Level 5'), 
            level_6=category.get('Level 6'), 
            level_7=category.get('Level 7')  
        )
    else:
        return CategoryResponse(
            product_id=product.product_id,
            level_1=None,
            level_2=None,
            level_3=None,
            level_4=None, 
            level_5=None, 
            level_6=None, 
            level_7=None  
        )

@app.post("/get_product_mapping_async", response_model=ResponseModel)
async def get_product_mapping_async(products: List[Product]):
    """
    This function maps products to their corresponding categories asynchronously.
    :param products: List of products to be mapped.
    :return: Response model containing the mapped categories.
    """
    response_data = []

    def process_product_thread_safe(product):
        map_instance = category_generator()
        try:
            result = process_product(product, map_instance)
        except Exception as e:
            result = {"product_id": product.product_id, "error": str(e)}
        finally:
            map_instance.exit_generator()
        return result

    try:
        with concurrent.futures.ThreadPoolExecutor() as executor:
            futures = [executor.submit(process_product_thread_safe, product) for product in products]
            for future in concurrent.futures.as_completed(futures):
                response_data.append(future.result())

        return ResponseModel(
            status="Success",
            message="Successfully mapped categories.",
            data=response_data
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail={"status": "Error", "message": str(e), "data": {}})



def fetch_categories_as_dict():
    """
    This function fetches categories from the database and returns them as a dictionary.
    :return: Dictionary of categories.
    """
    try:
        db = database()
        results = db.get_categories_dict()
        categories_dict = {}

        for row in results:
            level1_id = row['level1_id']
            level2_id = row['level2_id']
            level3_id = row['level3_id']
            level4_id = row['level4_id']
            level5_id = row['level5_id']
            level6_id = row['level6_id']
            level7_id = row['level7_id']
            
            if level1_id not in categories_dict:
                categories_dict[level1_id] = {
                    'level1_id': level1_id,
                    'category_name': row['level1_name'],
                    'children': {}
                }
            
            if level2_id:
                if level2_id not in categories_dict[level1_id]['children']:
                    categories_dict[level1_id]['children'][level2_id] = {
                        'level2_id': level2_id,
                        'category_name': row['level2_name'],
                        'children': {}
                    }
            
            if level3_id:
                if level3_id not in categories_dict[level1_id]['children'][level2_id]['children']:
                    categories_dict[level1_id]['children'][level2_id]['children'][level3_id] = {
                        'level3_id': level3_id,
                        'category_name': row['level3_name'],
                        'children': {}
                    }

            if level4_id:
                if level4_id not in categories_dict[level1_id]['children'][level2_id]['children'][level3_id]['children']:
                    categories_dict[level1_id]['children'][level2_id]['children'][level3_id]['children'][level4_id] = {
                        'level4_id': level4_id,
                        'category_name': row['level4_name'],
                        'children': {}
                    }

            if level5_id:
                if level5_id not in categories_dict[level1_id]['children'][level2_id]['children'][level3_id]['children'][level4_id]['children']:
                    categories_dict[level1_id]['children'][level2_id]['children'][level3_id]['children'][level4_id]['children'][level5_id] = {
                        'level5_id': level5_id,
                        'category_name': row['level5_name'],
                        'children': {}
                    }

            if level6_id:
                if level6_id not in categories_dict[level1_id]['children'][level2_id]['children'][level3_id]['children'][level4_id]['children'][level5_id]['children']:
                    categories_dict[level1_id]['children'][level2_id]['children'][level3_id]['children'][level4_id]['children'][level5_id]['children'][level6_id] = {
                        'level6_id': level6_id,
                        'category_name': row['level6_name'],
                        'children': {}
                    }

            if level7_id:
                categories_dict[level1_id]['children'][level2_id]['children'][level3_id]['children'][level4_id]['children'][level5_id]['children'][level6_id]['children'][level7_id] = {
                    'level7_id': level7_id,
                    'category_name': row['level7_name']
                }

        # Building the final categories list with extended levels
        categories_list = [{
            'category_name': v['category_name'],
            'level1_id': v['level1_id'],
            'children': [{
                'category_name': c['category_name'],
                'level2_id': c['level2_id'],
                'children': [{
                    'category_name': l3['category_name'],
                    'level3_id': l3['level3_id'],
                    'children': [{
                        'category_name': l4['category_name'],
                        'level4_id': l4['level4_id'],
                        'children': [{
                            'category_name': l5['category_name'],
                            'level5_id': l5['level5_id'],
                            'children': [{
                                'category_name': l6['category_name'],
                                'level6_id': l6['level6_id'],
                                'children': list(l6['children'].values())
                            } for l6 in l5['children'].values()]
                        } for l5 in l4['children'].values()]
                    } for l4 in l3['children'].values()]
                } for l3 in c['children'].values()]
            } for c in v['children'].values()]
        } for v in categories_dict.values()]
        
        db.close_connection()

        return JSONResponse(content={
            "status": "Success",
            "message": "Categories fetched successfully",
            "data": categories_list
        }, status_code=200)

    except Exception as e:
        return JSONResponse(content={
            "status": "Error",
            "message": f"{str(e)}",
            "data": []
        }, status_code=500)



# def get_category_info(category_input):
#     """
#     This function fetches the parent information from the database based on the input category name.
#     :param category_input: The category for which the parent categories are to be fetched.
#     :return: A JSON response containing the parent categories.
#     """
#     try:
#         db = database()
#         category_parts = [part.strip() for part in category_input.split('>')]
#         category_name = category_parts[-1]
#         parent_name = category_parts[-2] if len(category_parts) > 1 else None
#         grandparent_name = category_parts[-3] if len(category_parts) > 2 else None

#         results = db.get_categoryname_info(category_name)

#         if not results:
#             raise HTTPException(status_code=404, detail=f"Category '{category_name}' not found.")


#         def match_hierarchy(result):
#             if result['level'] == 'level1':
#                 return True
#             elif result['level'] == 'level2':
#                 return not parent_name or result['level1_parent_name'] == parent_name
#             elif result['level'] == 'level3':
#                 return (not parent_name or result['level2_parent_name'] == parent_name) and \
#                     (not grandparent_name or result['level1_parent_name'] == grandparent_name)

#         filtered_results = list(filter(match_hierarchy, results))

#         if not filtered_results:
#             raise HTTPException(status_code=404, detail=f"No category '{category_name}' found with specified parent.")


#         if len(filtered_results) > 1:
#             combinations = []
#             for result in filtered_results:
#                 if result['level'] == 'level1':
#                     combinations.append(result['category_name'])
#                 elif result['level'] == 'level2':
#                     combinations.append(str(f"{result['level1_parent_name']} > {result['category_name']}"))
#                 elif result['level'] == 'level3':
#                     combinations.append(str(f"{result['level1_parent_name']} > {result['level2_parent_name']} > {result['category_name']}"))
#             return JSONResponse(content={
#                 "status": "Error",
#                 "message": "There are multiple categories available with the same category name.",
#                 "data": combinations
#             }, status_code=200)

#         result = filtered_results[0]
#         db.close_connection()
#         if result['level'] == 'level1':
#             return JSONResponse(content={
#                 "status": "Success",
#                 "message": "Fetched the parent category successfully.",
#                 "data": [{"Level 1": result['category_name']}]
#             }, status_code=200)
#         elif result['level'] == 'level2':
#             return JSONResponse(content={
#                 "status": "Success",
#                 "message": "Fetched the parent category successfully.",
#                 "data": [{"Level 1": result['level1_parent_name'], "Level 2": result['category_name']}]
#             }, status_code=200)
        
#         elif result['level'] == 'level3':
#             return JSONResponse(content={
#                 "status": "Success",
#                 "message": "Fetched the parent category successfully.",
#                 "data": [{"Level 1": result['level1_parent_name'], "Level 2": result['level2_parent_name'], "Level 3": result['category_name']}]
#             }, status_code=200)
        
#     except HTTPException as e:
#         raise e
#     except Exception as e:
#         return JSONResponse(content={
#             "status": "Error",
#             "message": f"{str(e)}",
#             "data": []
#         }, status_code=500)

def get_category_info(category_input):
    """
    This function fetches the parent information from the database based on the input category name.
    :param category_input: The category for which the parent categories are to be fetched.
    :return: A JSON response containing the parent categories.
    """
    try:
        db = database()

        # Split the category input to get the individual category parts
        category_parts = [part.strip() for part in category_input.split('>')]
        category_name = category_parts[-1]  # The last part is the category name
        # Set parent and grandparent for all possible levels
        parent_name = category_parts[-2] if len(category_parts) > 1 else None
        grandparent_name = category_parts[-3] if len(category_parts) > 2 else None
        great_grandparent_name = category_parts[-4] if len(category_parts) > 3 else None
        great_great_grandparent_name = category_parts[-5] if len(category_parts) > 4 else None
        great_great_great_grandparent_name = category_parts[-6] if len(category_parts) > 5 else None

        # Fetch the category information based on the category name
        results = db.get_categoryname_info(category_name)

        if not results:
            raise HTTPException(status_code=404, detail=f"Category '{category_name}' not found.")

        def match_hierarchy(result):
            """ Match the hierarchy of category with its parents. """
            if result['level'] == 'level1':
                return True
            elif result['level'] == 'level2':
                return not parent_name or result['level1_parent_name'] == parent_name
            elif result['level'] == 'level3':
                return (not parent_name or result['level2_parent_name'] == parent_name) and \
                       (not grandparent_name or result['level1_parent_name'] == grandparent_name)
            elif result['level'] == 'level4':
                return (not parent_name or result['level3_parent_name'] == parent_name) and \
                       (not grandparent_name or result['level2_parent_name'] == grandparent_name) and \
                       (not great_grandparent_name or result['level1_parent_name'] == great_grandparent_name)
            elif result['level'] == 'level5':
                return (not parent_name or result['level4_parent_name'] == parent_name) and \
                       (not grandparent_name or result['level3_parent_name'] == grandparent_name) and \
                       (not great_grandparent_name or result['level2_parent_name'] == great_grandparent_name) and \
                       (not great_great_grandparent_name or result['level1_parent_name'] == great_great_grandparent_name)
            elif result['level'] == 'level6':
                return (not parent_name or result['level5_parent_name'] == parent_name) and \
                       (not grandparent_name or result['level4_parent_name'] == grandparent_name) and \
                       (not great_grandparent_name or result['level3_parent_name'] == great_grandparent_name) and \
                       (not great_great_grandparent_name or result['level2_parent_name'] == great_great_grandparent_name) and \
                       (not great_great_great_grandparent_name or result['level1_parent_name'] == great_great_great_grandparent_name)
            elif result['level'] == 'level7':
                return (not parent_name or result['level6_parent_name'] == parent_name) and \
                       (not grandparent_name or result['level5_parent_name'] == grandparent_name) and \
                       (not great_grandparent_name or result['level4_parent_name'] == great_grandparent_name) and \
                       (not great_great_grandparent_name or result['level3_parent_name'] == great_great_grandparent_name) and \
                       (not great_great_great_grandparent_name or result['level2_parent_name'] == great_great_great_grandparent_name)

        # Filter the results based on matching hierarchy
        filtered_results = list(filter(match_hierarchy, results))

        if not filtered_results:
            raise HTTPException(status_code=404, detail=f"No category '{category_name}' found with specified parent.")

        # If multiple matches found, return combinations
        if len(filtered_results) > 1:
            combinations = []
            for result in filtered_results:
                # Generate category paths for each level
                if result['level'] == 'level1':
                    combinations.append(result['category_name'])
                elif result['level'] == 'level2':
                    combinations.append(f"{result['level1_parent_name']} > {result['category_name']}")
                elif result['level'] == 'level3':
                    combinations.append(f"{result['level1_parent_name']} > {result['level2_parent_name']} > {result['category_name']}")
                elif result['level'] == 'level4':
                    combinations.append(f"{result['level1_parent_name']} > {result['level2_parent_name']} > {result['level3_parent_name']} > {result['category_name']}")
                elif result['level'] == 'level5':
                    combinations.append(f"{result['level1_parent_name']} > {result['level2_parent_name']} > {result['level3_parent_name']} > {result['level4_parent_name']} > {result['category_name']}")
                elif result['level'] == 'level6':
                    combinations.append(f"{result['level1_parent_name']} > {result['level2_parent_name']} > {result['level3_parent_name']} > {result['level4_parent_name']} > {result['level5_parent_name']} > {result['category_name']}")
                elif result['level'] == 'level7':
                    combinations.append(f"{result['level1_parent_name']} > {result['level2_parent_name']} > {result['level3_parent_name']} > {result['level4_parent_name']} > {result['level5_parent_name']} > {result['level6_parent_name']} > {result['category_name']}")
            return JSONResponse(content={
                "status": "Error",
                "message": "Multiple categories found with the same category name.",
                "data": combinations
            }, status_code=200)

        # Only one result - return the matched category info
        result = filtered_results[0]
        db.close_connection()

        # Return the matched category information up to level 7
        if result['level'] == 'level1':
            return JSONResponse(content={
                "status": "Success",
                "message": "Fetched the parent category successfully.",
                "data": [{"Level 1": result['category_name']}]
            }, status_code=200)
        elif result['level'] == 'level2':
            return JSONResponse(content={
                "status": "Success",
                "message": "Fetched the parent category successfully.",
                "data": [{"Level 1": result['level1_parent_name'], "Level 2": result['category_name']}]
            }, status_code=200)
        elif result['level'] == 'level3':
            return JSONResponse(content={
                "status": "Success",
                "message": "Fetched the parent category successfully.",
                "data": [{"Level 1": result['level1_parent_name'], "Level 2": result['level2_parent_name'], "Level 3": result['category_name']}]
            }, status_code=200)
        elif result['level'] == 'level4':
            return JSONResponse(content={
                "status": "Success",
                "message": "Fetched the parent category successfully.",
                "data": [{"Level 1": result['level1_parent_name'], "Level 2": result['level2_parent_name'], "Level 3": result['level3_parent_name'], "Level 4": result['category_name']}]
            }, status_code=200)
        elif result['level'] == 'level5':
            return JSONResponse(content={
                "status": "Success",
                "message": "Fetched the parent category successfully.",
                "data": [{"Level 1": result['level1_parent_name'], "Level 2": result['level2_parent_name'], "Level 3": result['level3_parent_name'], "Level 4": result['level4_parent_name'], "Level 5": result['category_name']}]
            }, status_code=200)
        elif result['level'] == 'level6':
            return JSONResponse(content={
                "status": "Success",
                "message": "Fetched the parent category successfully.",
                "data": [{"Level 1": result['level1_parent_name'], "Level 2": result['level2_parent_name'], "Level 3": result['level3_parent_name'], "Level 4": result['level4_parent_name'], "Level 5": result['level5_parent_name'], "Level 6": result['category_name']}]
            }, status_code=200)
        elif result['level'] == 'level7':
            return JSONResponse(content={
                "status": "Success",
                "message": "Fetched the parent category successfully.",
                "data": [{"Level 1": result['level1_parent_name'], "Level 2": result['level2_parent_name'], "Level 3": result['level3_parent_name'], "Level 4": result['level4_parent_name'], "Level 5": result['level5_parent_name'], "Level 6": result['level6_parent_name'], "Level 7": result['category_name']}]
            }, status_code=200)

    except HTTPException as e:
        raise e
    except Exception as e:
        return JSONResponse(content={
            "status": "Error",
            "message": f"{str(e)}",
            "data": []
        }, status_code=500)


class Mapping(BaseModel):
    level_1: Optional[str] = None
    level_2: Optional[str] = None
    level_3: Optional[str] = None
    level_4: Optional[str] = None
    level_5: Optional[str] = None
    level_6: Optional[str] = None
    level_7: Optional[str] = None

class HardLogicRequest(BaseModel):
    action: str = Field(..., pattern="^(create|update|delete)$")
    is_pattern: Optional[int] = None
    hard_logic_word: str
    new_hard_logic_word: Optional[str] = None
    Mappings: Optional[List[Mapping]] = None

class HardLogicResponse(BaseModel):
    status: str
    message: str

def raise_http_exception(status_code: int, message: str):
    """
    Raises an HTTP exception with the given status code and message.
    """
    logger.error(f"Error {status_code}: {message}")
    raise HTTPException(
        status_code=status_code,
        detail={"status": "Error", "message": message.capitalize(), "data": []}
    )

@app.exception_handler(HTTPException)
async def custom_http_exception_handler(request: Request, exc: HTTPException):
    """
    Custom exception handler for HTTP exceptions.
    """
    response_content = {
        "status": "Error",
        "message": exc.detail["message"],
        "data": exc.detail["data"]
    }
    return JSONResponse(status_code=exc.status_code, content=response_content)


@app.post("/modify_hard_logic", response_model=HardLogicResponse)
def modify_hard_logic(request: HardLogicRequest):
    """
    Modifies the hard logic of a given word.
    """
    try:
        db = database()

        # Check if the hard logic rule already exists for update and delete actions
        existing_hard_logic = db.check_existing_hard_logic(request.hard_logic_word)
        if request.action in ["update", "delete"] and not existing_hard_logic:
            raise_http_exception(status.HTTP_400_BAD_REQUEST, "Hard logic rule not found.")

        if request.action == "create":
            if existing_hard_logic:
                raise_http_exception(status.HTTP_400_BAD_REQUEST, "Hard logic rule already exists for this word.")
            
            # Fetch IDs for levels 1 through 7
            level1_id, level2_id, level3_id, level4_id, level5_id, level6_id, level7_id = db.get_category_ids(
                request.Mappings[0].level_1,
                request.Mappings[0].level_2,
                request.Mappings[0].level_3,
                request.Mappings[0].level_4,
                request.Mappings[0].level_5,
                request.Mappings[0].level_6,
                request.Mappings[0].level_7
            )
            # Create a new hard logic entry with levels 1 through 7
            db.create_hard_logic(request.hard_logic_word, request.is_pattern, level1_id, level2_id, level3_id, level4_id, level5_id, level6_id, level7_id)
            db.close_connection()
            response = HardLogicResponse(status="Success", message="Hard logic rule created.")
            return JSONResponse(content=response.dict(), status_code=status.HTTP_201_CREATED)

        elif request.action == "update":
            new_hard_logic_word = request.new_hard_logic_word or existing_hard_logic['word']
            is_pattern = request.is_pattern if request.is_pattern is not None else existing_hard_logic['is_pattern']
            # Get existing level IDs
            level1_id, level2_id, level3_id = existing_hard_logic['level1_id'], existing_hard_logic['level2_id'], existing_hard_logic['level3_id']
            level4_id, level5_id, level6_id, level7_id = existing_hard_logic.get('level4_id'), existing_hard_logic.get('level5_id'), existing_hard_logic.get('level6_id'), existing_hard_logic.get('level7_id')

            if request.Mappings:
                # Fetch updated level IDs if mappings are provided
                level1_id, level2_id, level3_id, level4_id, level5_id, level6_id, level7_id = db.get_category_ids(
                    request.Mappings[0].level_1,
                    request.Mappings[0].level_2,
                    request.Mappings[0].level_3,
                    request.Mappings[0].level_4,
                    request.Mappings[0].level_5,
                    request.Mappings[0].level_6,
                    request.Mappings[0].level_7
                )

            # Update the hard logic entry
            db.update_hard_logic(new_hard_logic_word, is_pattern, level1_id, level2_id, level3_id, level4_id, level5_id, level6_id, level7_id, request.hard_logic_word)
            db.close_connection()
            response = HardLogicResponse(status="Success", message="Hard logic rule updated.")
            return JSONResponse(content=response.dict(), status_code=status.HTTP_200_OK)

        elif request.action == "delete":
            if request.Mappings:
                raise_http_exception(status.HTTP_400_BAD_REQUEST, "Mappings should not be passed for deletion.")
            
            # Delete the hard logic entry
            db.delete_hard_logic(request.hard_logic_word)
            db.close_connection()
            response = HardLogicResponse(status="Success", message="Hard logic rule deleted.")
            return JSONResponse(content=response.dict(), status_code=status.HTTP_200_OK)

        else:
            raise_http_exception(status.HTTP_400_BAD_REQUEST, "Invalid action specified")

    except HTTPException as e:
        logger.error(f"HTTPException: {e.detail}")
        raise   
    except ValueError as e:
        raise_http_exception(status.HTTP_400_BAD_REQUEST, str(e))
    except Exception as e:
        logger.error(f"Internal Server Error: {str(e)}\n{traceback.format_exc()}")
        raise_http_exception(status.HTTP_500_INTERNAL_SERVER_ERROR, str(e))



class SoftLogicRequest(BaseModel):
    action: str = Field(..., pattern="^(create|update|delete)$")
    soft_logic_word: str
    new_soft_logic_word: Optional[str] = None
    Mappings: Optional[List[Mapping]] = None

class SoftLogicResponse(BaseModel):
    status: str
    message: str

@app.post("/modify_soft_logic", response_model=SoftLogicResponse)
class SoftLogicRequest(BaseModel):
    action: str = Field(..., pattern="^(create|update|delete)$")
    soft_logic_word: str
    new_soft_logic_word: Optional[str] = None
    Mappings: Optional[List[Mapping]] = None  # Mapping should support levels 1 to 7

class SoftLogicResponse(BaseModel):
    status: str
    message: str

@app.post("/modify_soft_logic", response_model=SoftLogicResponse)
def modify_soft_logic(request: SoftLogicRequest):
    """
    Modify a soft logic rule.
    """
    try:
        db = database()
        # For update action, fetch existing record based on soft_logic_word
        existing_soft_logic = db.check_existing_soft_logic(request.soft_logic_word)
        if request.action in ["update", "delete"] and not existing_soft_logic:
            raise_http_exception(status.HTTP_400_BAD_REQUEST, "Soft logic rule not found.")

        if request.action == "create":
            # Ensure only one soft logic row exists for a word
            if existing_soft_logic:
                raise_http_exception(status.HTTP_400_BAD_REQUEST, "Soft logic rule already exists for this keyword.")
            
            # Fetch level IDs (from level 1 to level 7)
            level1_id, level2_id, level3_id, level4_id, level5_id, level6_id, level7_id = db.get_category_ids(
                request.Mappings[0].level_1,
                request.Mappings[0].level_2,
                request.Mappings[0].level_3,
                request.Mappings[0].level_4,
                request.Mappings[0].level_5,
                request.Mappings[0].level_6,
                request.Mappings[0].level_7
            )
            db.create_soft_logic(request.soft_logic_word, level1_id, level2_id, level3_id, level4_id, level5_id, level6_id, level7_id)
            db.close_connection()
            response = SoftLogicResponse(status="Success", message="Soft logic rule created.")
            return JSONResponse(content=response.dict(), status_code=status.HTTP_201_CREATED)

        elif request.action == "update":
            new_soft_logic_word = request.new_soft_logic_word or existing_soft_logic['Keyword']
            level1_id, level2_id, level3_id = existing_soft_logic['level1_id'], existing_soft_logic['level2_id'], existing_soft_logic['level3_id']
            level4_id, level5_id, level6_id, level7_id = existing_soft_logic['level4_id'], existing_soft_logic['level5_id'], existing_soft_logic['level6_id'], existing_soft_logic['level7_id']

            if request.Mappings:
                # Fetch level IDs (from level 1 to level 7)
                level1_id, level2_id, level3_id, level4_id, level5_id, level6_id, level7_id = db.get_category_ids(
                    request.Mappings[0].level_1,
                    request.Mappings[0].level_2,
                    request.Mappings[0].level_3,
                    request.Mappings[0].level_4,
                    request.Mappings[0].level_5,
                    request.Mappings[0].level_6,
                    request.Mappings[0].level_7
                )

            db.update_soft_logic(new_soft_logic_word, level1_id, level2_id, level3_id, level4_id, level5_id, level6_id, level7_id, request.soft_logic_word)
            db.close_connection()
            response = SoftLogicResponse(status="Success", message="Soft logic rule updated.")
            return JSONResponse(content=response.dict(), status_code=status.HTTP_200_OK)

        elif request.action == "delete":
            if request.Mappings:
                raise_http_exception(status.HTTP_400_BAD_REQUEST, "Mappings should not be passed for deletion.")
            
            db.delete_soft_logic(request.soft_logic_word)
            db.close_connection()
            response = SoftLogicResponse(status="Success", message="Soft logic rule deleted.")
            return JSONResponse(content=response.dict(), status_code=status.HTTP_200_OK)

        else:
            raise_http_exception(status.HTTP_400_BAD_REQUEST, "Invalid action specified")

    except HTTPException as e:
        logger.error(f"HTTPException: {e.detail}")
        raise   
    except ValueError as e:
        raise_http_exception(status.HTTP_400_BAD_REQUEST, str(e))
    except Exception as e:
        logger.error(f"Internal Server Error: {str(e)}\n{traceback.format_exc()}")
        raise_http_exception(status.HTTP_500_INTERNAL_SERVER_ERROR, str(e))

class HardLogic_Output(BaseModel):
    is_pattern: int
    level_1: Optional[str] = None
    level_2: Optional[str] = None
    level_3: Optional[str] = None
    level_4: Optional[str] = None
    level_5: Optional[str] = None
    level_6: Optional[str] = None
    level_7: Optional[str] = None


@app.get("/get_hardlogic_with_word",response_model=ResponseModel)
def get_hardlogic_with_word(word: str):
    """
    Retrieves hard logic rules that match the given word.
    :param word: Hard logic word.
    :return: JSON response containing the hard logic rules.
    """
    response_data = []
    try:
        db = database()
        response = db.get_category_hardlogic_word(word)  # Ensure this function returns up to 7 levels
        db.close_connection()

        if response:
            # Extend the response to include up to level 7
            response_data.append(HardLogic_Output(
                is_pattern=response[0],
                level_1=response[1],
                level_2=response[2],
                level_3=response[3],
                level_4=response[4],  # Handle level 4 if present
                level_5=response[5],  # Handle level 5 if present
                level_6=response[6],  # Handle level 6 if present
                level_7=response[7]   # Handle level 7 if present
            ))

            return ResponseModel(
                status="Success",
                message=f"Hard logic found for the word: {word}",
                data=response_data
            )
        else:
            raise_http_exception(status.HTTP_400_BAD_REQUEST, "No hard logic found.")

    except HTTPException as e:
        logger.error(f"HTTPException: {e.detail}")
        raise
    except ValueError as e:
        raise_http_exception(status.HTTP_400_BAD_REQUEST, str(e))
    except Exception as e:
        raise_http_exception(status.HTTP_500_INTERNAL_SERVER_ERROR, str(e))


@app.get("/get_softlogic_with_word",response_model=ResponseModel)
def get_softlogic_with_word(keyword: str):
    """
    Retrieves soft logic rules that match the given keyword.
    :param keyword: Soft logic keyword.
    :return: JSON response containing the soft logic rules.
    """
    response_data = []
    try:
        db = database()
        response = db.fetch_category_kf(keyword)  # This function must now return up to 7 levels
        db.close_connection()

        if response:
            # Extend mapping to include levels 4 to 7
            response_data.append(Mapping(
                level_1=response[0],
                level_2=response[1],
                level_3=response[2],
                level_4=response[3],  # Handle level 4 if present
                level_5=response[4],  # Handle level 5 if present
                level_6=response[5],  # Handle level 6 if present
                level_7=response[6]   # Handle level 7 if present
            ))

            return ResponseModel(
                status="Success",
                message=f"Soft logic found for the word: {keyword}",
                data=response_data
            )
        else:
            raise_http_exception(status.HTTP_400_BAD_REQUEST, "No soft logic found.")

    except HTTPException as e:
        logger.error(f"HTTPException: {e.detail}")
        raise
    except ValueError as e:
        raise_http_exception(status.HTTP_400_BAD_REQUEST, str(e))
    except Exception as e:
        raise_http_exception(status.HTTP_500_INTERNAL_SERVER_ERROR, str(e))

        

@app.get("/get_all_categories")
def get_categories_json():
    """
    Retrieves all categories from the database.
    :return: JSON response containing all categories.
    """
    return fetch_categories_as_dict()

@app.get("/get_parents")
def get_category(category_input: str):
    """
    Retrieves the parent category of the given category.
    :param category_input: Category name.
    :return: JSON response containing the parent category.
    """
    result = get_category_info(category_input)
    return result