"""
Centralized Logging Configuration for Category Mapping System

This module provides structured logging with error tracking, performance monitoring,
and audit trails for the CrewAI-based category mapping workflow.
"""

import logging
import logging.handlers
import json
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional
from enum import Enum


class LogLevel(Enum):
    """Standard log levels with semantic meaning"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class ErrorCategory(Enum):
    """Categories of errors for better tracking"""
    DATABASE = "DATABASE"
    API = "API"
    AGENT = "AGENT"
    TOOL = "TOOL"
    VALIDATION = "VALIDATION"
    NETWORK = "NETWORK"
    CONFIGURATION = "CONFIGURATION"
    UNKNOWN = "UNKNOWN"


class StructuredLogger:
    """
    Structured logger for the category mapping system.
    Provides consistent logging format with metadata support.
    """
    
    def __init__(self, name: str = "CategoryMapper", log_dir: str = "logs"):
        """
        Initialize the structured logger.
        
        Args:
            name: Logger name
            log_dir: Directory for log files
        """
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True) 
        
        # Remove existing handlers to avoid duplicates
        self.logger.handlers = []
        
        # Setup handlers
        self._setup_file_handler()
        self._setup_console_handler()
        self._setup_error_file_handler()
        
    def _setup_file_handler(self):
        """Setup rotating file handler for all logs"""
        log_file = self.log_dir / f"category_mapping_{datetime.now().strftime('%Y%m%d')}.log"
        
        # Rotating file handler (10MB per file, keep 5 backups)
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        
        # JSON formatter for structured logs
        file_formatter = JsonFormatter()
        file_handler.setFormatter(file_formatter)
        
        self.logger.addHandler(file_handler)
        
    def _setup_console_handler(self):
        """Setup console handler for immediate feedback"""
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        # Human-readable format for console
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        console_handler.setFormatter(console_formatter)
        
        self.logger.addHandler(console_handler)
        
    def _setup_error_file_handler(self):
        """Setup separate file handler for errors only"""
        error_file = self.log_dir / f"errors_{datetime.now().strftime('%Y%m%d')}.log"
        
        error_handler = logging.handlers.RotatingFileHandler(
            error_file,
            maxBytes=5 * 1024 * 1024,  # 5MB
            backupCount=10,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        
        error_formatter = JsonFormatter()
        error_handler.setFormatter(error_formatter)
        
        self.logger.addHandler(error_handler)

    
    def log_event(self, 
                  level: LogLevel,
                  message: str,
                  category: Optional[ErrorCategory] = None,
                  product_id: Optional[int] = None,
                  agent_name: Optional[str] = None,
                  tool_name: Optional[str] = None,
                  error_details: Optional[Dict[str, Any]] = None,
                  **kwargs):
        """
        Log an event with structured metadata.
        
        Args:
            level: Log level
            message: Log message
            category: Error category for classification
            product_id: Product being processed
            agent_name: Agent that generated the event
            tool_name: Tool that generated the event
            error_details: Additional error context
            **kwargs: Additional metadata
        """
        extra = {
            'timestamp': datetime.now().isoformat(),
            'category': category.value if category else None,
            'product_id': product_id,
            'agent_name': agent_name,
            'tool_name': tool_name,
            'error_details': error_details,
            'metadata': kwargs
        }
        
        # Remove None values
        extra = {k: v for k, v in extra.items() if v is not None}
        
        # Log based on level
        log_method = getattr(self.logger, level.value.lower())
        log_method(message, extra=extra)
    
    def log_error(self, 
                  exception: Exception,
                  message: str,
                  category: ErrorCategory = ErrorCategory.UNKNOWN,
                  product_id: Optional[int] = None,
                  agent_name: Optional[str] = None,
                  tool_name: Optional[str] = None,
                  **kwargs):
        """
        Log an error with exception details.
        
        Args:
            exception: The exception that occurred
            message: Context message
            category: Error category
            product_id: Product being processed
            agent_name: Agent where error occurred
            tool_name: Tool where error occurred
            **kwargs: Additional metadata
        """
        import traceback
        
        error_details = {
            'exception_type': type(exception).__name__,
            'exception_message': str(exception),
            'traceback': traceback.format_exc()
        }
        
        self.log_event(
            LogLevel.ERROR,
            message,
            category=category,
            product_id=product_id,
            agent_name=agent_name,
            tool_name=tool_name,
            error_details=error_details,
            **kwargs
        )
    
   
 
class JsonFormatter(logging.Formatter):
    """
    Custom JSON formatter for structured logging.
    """
    
    def format(self, record):
        """Format log record as JSON"""
        log_obj = {
            'timestamp': datetime.now().isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Add extra fields if present
        if hasattr(record, 'product_id'):
            log_obj['product_id'] = record.product_id
        if hasattr(record, 'category'):
            log_obj['category'] = record.category
        if hasattr(record, 'agent_name'):
            log_obj['agent_name'] = record.agent_name
        if hasattr(record, 'tool_name'):
            log_obj['tool_name'] = record.tool_name
        if hasattr(record, 'error_details'):
            log_obj['error_details'] = record.error_details
        if hasattr(record, 'metadata'):
            log_obj['metadata'] = record.metadata
            
        # Add exception info if present
        if record.exc_info:
            log_obj['exception'] = self.formatException(record.exc_info)
            
        return json.dumps(log_obj, default=str)


# Global logger instance
_logger_instance = None

def get_logger() -> StructuredLogger:
    """
    Get or create the global logger instance.
    
    Returns:
        StructuredLogger: The global logger instance
    """
    global _logger_instance
    if _logger_instance is None:
        _logger_instance = StructuredLogger()
    return _logger_instance


# Convenience functions
def log_info(message: str, **kwargs):
    """Log an info message"""
    get_logger().log_event(LogLevel.INFO, message, **kwargs)

def log_warning(message: str, **kwargs):
    """Log a warning message"""
    get_logger().log_event(LogLevel.WARNING, message, **kwargs)

def log_error(exception: Exception, message: str, **kwargs):
    """Log an error with exception"""
    get_logger().log_error(exception, message, **kwargs)

def log_debug(message: str, **kwargs):
    """Log a debug message"""
    get_logger().log_event(LogLevel.DEBUG, message, **kwargs)

 