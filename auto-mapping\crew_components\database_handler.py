"""
Database Handler for Category Mapping System

This module handles database operations including updating mapping status,
storing category paths, and logging mapping failures.
"""

import os
import json
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime
from typing import Dict, Optional, Any, List
from dotenv import load_dotenv

from .error_handlers import safe_database_operation, ProtectedExecution, DatabaseError
from .logging_config import get_logger, ErrorCategory, LogLevel

load_dotenv()
logger = get_logger()


class DatabaseHandler:
    """
    Handles all database operations for the category mapping system.
    """
    
    def __init__(self):
        """Initialize database connection parameters."""
        self.db_config = {
            'host': os.getenv('POSTGRES_HOST', 'localhost'),
            'port': os.getenv('POSTGRES_PORT', '5433'),
            'database': os.getenv('POSTGRES_DB', 'aicategorymapping'),
            'user': os.getenv('POSTGRES_USER', 'cat_manager'),
            'password': os.getenv('POSTGRES_PASSWORD')
        }
        self.conn = None
        self.cursor = None
        
    def __enter__(self):
        """Context manager entry - establish database connection."""
        self.connect()
        return self
        
    def __exit__(self):
        """Context manager exit - close database connection."""
        self.disconnect()
        
    @safe_database_operation("connect", product_id=None)
    def connect(self):
        """Establish database connection with simple retry/backoff to tolerate tunnel warm-up."""
        import time
        max_attempts = int(os.getenv('DB_CONNECT_RETRIES', '1'))
        delay_seconds = float(os.getenv('DB_CONNECT_RETRY_DELAY', '1.5'))
        last_err = None
        for attempt in range(1, max_attempts + 1):
            try:
                self.conn = psycopg2.connect(**self.db_config)
                self.cursor = self.conn.cursor(cursor_factory=RealDictCursor)
                logger.log_event(
                    LogLevel.INFO,
                    f"Database connection established (attempt {attempt}/{max_attempts})",
                    category=ErrorCategory.DATABASE
                )
                return
            except psycopg2.Error as e:
                last_err = e
                logger.log_error(
                    e,
                    f"Failed to connect to database (attempt {attempt}/{max_attempts})",
                    category=ErrorCategory.DATABASE,
                    host=self.db_config['host'],
                    port=self.db_config['port']
                )
                if attempt < max_attempts:
                    time.sleep(delay_seconds)
                else:
                    break
        # If we get here, all attempts failed
        raise DatabaseError(f"Database connection failed after {max_attempts} attempts: {last_err}") from last_err
            
    @safe_database_operation("disconnect", product_id=None)
    def disconnect(self):
        """Close database connection."""
        if self.cursor:
            self.cursor.close()
            self.cursor = None
            
        if self.conn:
            self.conn.close()
            self.conn = None
            
        logger.log_event(
            LogLevel.DEBUG,
            "Database connection closed",
            category=ErrorCategory.DATABASE
        )
        
        
       
  

# Singleton instance
_db_handler = None

def get_database_handler() -> DatabaseHandler:
    """Get or create the singleton database handler."""
    global _db_handler
    if _db_handler is None:
        _db_handler = DatabaseHandler()
    return _db_handler


