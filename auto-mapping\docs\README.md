# Category Mapping Auto-Mapping System

## 🎯 Overview

The Category Mapping Auto-Mapping System is a high-performance, AI-powered solution for automatically mapping products to a 7-level category hierarchy. Built with CrewAI agents, FAISS vector search, and PostgreSQL integration, it provides scalable, accurate category mapping with comprehensive error handling and performance optimization.

## 🏗️ Architecture

### System Components

```
┌─────────────────────────────────────────────────────────────────┐
│                    Category Mapping System                      │
├─────────────────────────────────────────────────────────────────┤
│  🤖 CrewAI Agents                                              │
│  ├── Mapper Agent (Product → Category)                         │
│  └── Validation Agent (Quality Assurance)                      │
├─────────────────────────────────────────────────────────────────┤
│  🔍 Search Tools                                               │
│  ├── FAISS Vector Search (Semantic)                            │
│  ├── PostgreSQL Search (Keyword)                               │
│  └── Hybrid Search (Combined)                                  │
├─────────────────────────────────────────────────────────────────┤
│  ⚡ Performance Layer                                           │
│  ├── Parallel Processing                                       │
│  ├── Batch Operations                                          │
│  ├── Caching Strategies                                        │
│  └── Performance Monitoring                                    │
├─────────────────────────────────────────────────────────────────┤
│  🗄️ Data Layer                                                 │
│  ├── PostgreSQL Database                                       │
│  ├── FAISS Index Files                                         │
│  └── Category Embeddings                                       │
├─────────────────────────────────────────────────────────────────┤
│  🛡️ Reliability Layer                                          │
│  ├── Error Handling & Recovery                                 │
│  ├── Structured Logging                                        │
│  ├── Performance Profiling                                     │
│  └── Comprehensive Testing                                     │
└─────────────────────────────────────────────────────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Python 3.9+
- PostgreSQL database
- OpenAI API key
- 8GB+ RAM (for FAISS embeddings)

### Installation
```bash
# Clone and setup
cd auto-mapping
pip install -r requirements.txt

# Configure environment
cp environment_template.txt .env
# Edit .env with your database and API credentials

# Generate embeddings (first time only)
python generate_embeddings.py

# Run single product mapping
python auto_mapping.py

# Run batch processing
python batch_processor.py

# Run performance analysis
python analyze_performance.py
```

## 📊 Performance Metrics

### Current Performance (After Optimization)
- **Throughput:** 1.24 products/second (end-to-end)
- **FAISS Search:** 764ms average (optimized from 1000ms+)
- **Agent Execution:** ~10ms mapper, ~5ms validation
- **Parallel Speedup:** 1.26x faster (20.8% time savings)
- **Test Coverage:** 37.53% overall, 100% for core agents

### Optimization Features
- **Parallel Processing:** 4 FAISS workers, 2 agent workers
- **FAISS Index Options:** Flat, IVFFlat, IVFPQ, HNSW (7ms-58ms range)
- **Database Connection Pooling:** Async operations
- **Comprehensive Monitoring:** Real-time performance tracking

## 🧪 Testing

### Test Suite Overview
- **198 total tests** across unit, integration, and E2E
- **143/192 passing** (74% success rate)
- **6 test categories:** Setup, Agents, Tools, Batch, Database, Integration

### Running Tests
```bash
# All tests
pytest

# Specific categories
pytest -m unit          # Unit tests only
pytest -m integration   # Integration tests only
pytest -m e2e          # End-to-end tests only

# With coverage
pytest --cov --cov-report=html
```

## 📁 Project Structure

```
auto-mapping/
├── 📄 Core Scripts
│   ├── auto_mapping.py              # Main mapping orchestration
│   ├── batch_processor.py           # Batch processing with error resilience
│   ├── parallel_processor.py        # Parallel processing optimization
│   ├── performance_profiler.py      # Performance monitoring tools
│   ├── faiss_optimizer.py          # FAISS index optimization
│   └── analyze_performance.py      # Performance analysis script
├── 🤖 CrewAI Components
│   ├── crew_components/
│   │   ├── agents.py               # Mapper & Validation agents
│   │   ├── tasks.py                # CrewAI task definitions
│   │   ├── crew.py                 # Crew orchestration
│   │   ├── database_handler.py     # PostgreSQL operations
│   │   ├── error_handlers.py       # Error handling & recovery
│   │   ├── logging_config.py       # Structured logging
│   │   └── tools/                  # Search tools
│   │       ├── category_hybrid_search.py      # Hybrid search
│   │       ├── category_hierarchy_tool.py     # Keyword search
│   │       └── category_embeddings_manager.py # FAISS management
├── 🧪 Testing Infrastructure
│   ├── tests/
│   │   ├── unit/                   # Unit tests
│   │   ├── integration/            # Integration tests
│   │   ├── e2e/                    # End-to-end tests
│   │   ├── conftest.py            # Test fixtures
│   │   └── test_setup.py          # Setup verification
│   ├── pytest.ini                 # Test configuration
│   └── .coveragerc               # Coverage configuration
├── 📊 Data & Configuration
│   ├── embeddings/                 # FAISS embeddings & metadata
│   ├── performance_reports/        # Performance analysis reports
│   ├── requirements.txt           # Python dependencies
│   └── .env                      # Environment configuration
└── 📚 Documentation
    └── docs/                      # Comprehensive documentation
```

## 🔧 Configuration

### Environment Variables
```bash
# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5433
POSTGRES_DB=aicategorymapping
POSTGRES_USER=cat_manager
POSTGRES_PASSWORD=your_password

# AI Configuration
OPENAI_API_KEY=your_openai_key

# Performance Configuration
BATCH_SIZE=100
MAX_RETRIES=3
LOG_LEVEL=INFO
```

### Performance Tuning
```python
# FAISS Index Optimization
config = IndexOptimizationConfig(
    index_type="HNSW",    # Fastest: 7ms avg
    nlist=32,             # HNSW M parameter
    use_gpu=False         # CPU optimized
)

# Parallel Processing
orchestrator = ParallelWorkflowOrchestrator(
    faiss_workers=4,      # FAISS parallelization
    agent_workers=2,      # Agent parallelization
    db_pool_size=5        # Database connections
)
```

## 🚨 Error Handling

### Error Categories
- **DATABASE:** Connection, query, transaction errors
- **API:** OpenAI API, rate limiting, network errors
- **AGENT:** CrewAI execution, prompt errors
- **TOOL:** FAISS search, tool execution errors
- **VALIDATION:** Data validation, format errors

### Error Recovery
```python
# Automatic retry with exponential backoff
@safe_execution("operation_name", ErrorCategory.TOOL, max_retries=3)
def risky_operation():
    # Your code here
    pass

# Protected execution with error capture
with ProtectedExecution("operation", ErrorCategory.AGENT) as protected:
    result = perform_operation()
    protected.set_result(result)
```

## 📈 Monitoring & Logging

### Performance Monitoring
```python
# Profile any operation
with profiler.profile_operation("my_operation"):
    # Your code here
    pass

# Generate performance reports
report_file = profiler.generate_performance_report()
```

### Structured Logging
```python
# Event logging
logger.log_event(
    LogLevel.INFO,
    "Operation completed",
    category=ErrorCategory.TOOL,
    product_id=123
)

# Error logging
logger.log_error(
    exception,
    "Operation failed",
    category=ErrorCategory.DATABASE
)

# Performance logging
logger.log_performance(
    "operation_name",
    duration_ms=150.5,
    category=ErrorCategory.AGENT
)
```

## 🔄 Workflow Examples

### Single Product Mapping
```python
from auto_mapping import process_single_product

product = {
    "product_id": "12345",
    "product_name": "Gaming Laptop",
    "breadcrumbs": "Electronics > Computers > Gaming",
    "product_info": "High-performance gaming laptop with RTX graphics"
}

result = process_single_product(product, max_retries=3)
print(result)
# Output: {"status": "success", "category_path": "Electronics > Computers > Gaming Laptops", "confidence": 0.95}
```

### Batch Processing
```python
from batch_processor import BatchProcessor

processor = BatchProcessor(max_retries=3, batch_size=50)
processor.initialize()

products = [...]  # List of product dictionaries
results = processor.process_batch(products)

print(f"Processed: {results['total']}")
print(f"Success rate: {results['successful']}/{results['total']}")
```

### Parallel Processing
```python
from parallel_processor import ParallelWorkflowOrchestrator

orchestrator = ParallelWorkflowOrchestrator(
    faiss_workers=4,
    agent_workers=2,
    db_pool_size=5
)

results = orchestrator.process_products_parallel(
    products,
    enable_faiss_parallel=True,
    enable_agent_parallel=True
)

print(f"Throughput: {results['overall_throughput_per_second']:.2f} products/second")
```

## 📚 Additional Documentation

- **[Agent Documentation](docs/agents.md)** - CrewAI agents and prompt engineering
- **[Search Tools Documentation](docs/search_tools.md)** - FAISS and PostgreSQL integration
- **[Performance Guide](docs/performance.md)** - Optimization and monitoring
- **[Testing Guide](docs/testing.md)** - Test suite and coverage
- **[Error Handling Guide](docs/error_handling.md)** - Error recovery and logging
- **[API Reference](docs/api_reference.md)** - Complete API documentation

## 🤝 Contributing

### Development Workflow
1. **Setup:** Follow quick start guide
2. **Testing:** Run `pytest` before committing
3. **Performance:** Use `python analyze_performance.py` for benchmarking
4. **Documentation:** Update relevant docs with changes
5. **Review:** Ensure code coverage and documentation completeness

### Code Standards
- **Type Hints:** All functions must have type annotations
- **Docstrings:** All classes and functions must be documented
- **Error Handling:** Use structured error handling patterns
- **Testing:** Maintain >70% code coverage
- **Performance:** Profile performance-critical changes

## 📞 Support

### Troubleshooting
- **Database Connection Issues:** Check PostgreSQL configuration and credentials
- **FAISS Errors:** Ensure embeddings are generated and accessible
- **Performance Issues:** Run performance analysis and check resource usage
- **Test Failures:** Check test logs and ensure proper environment setup

### Performance Optimization
- **FAISS Slow:** Consider HNSW index (7ms vs 58ms Flat)
- **Low Throughput:** Enable parallel processing (1.26x speedup)
- **High Memory:** Use IVFPQ quantization (50-80% reduction)
- **Database Slow:** Enable connection pooling and async operations

---

**Built with ❤️ using CrewAI, FAISS, and PostgreSQL**
