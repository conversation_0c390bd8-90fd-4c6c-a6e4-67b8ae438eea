# Testing Framework
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-mock>=3.11.1
pytest-asyncio>=0.21.1
pytest-timeout>=2.1.0
pytest-xdist>=3.3.1  # For parallel test execution
pytest-env>=1.0.0  # For environment variable management

# Mocking and Test Utilities
faker>=19.0.0  # For generating test data
factory-boy>=3.3.0  # For test fixtures
responses>=0.23.1  # For mocking HTTP requests
freezegun>=1.2.2  # For mocking datetime

# Code Quality
coverage>=7.2.7
coverage-badge>=1.1.0

# Database Testing
pytest-postgresql>=5.0.0  # For PostgreSQL testing
sqlalchemy-utils>=0.41.1  # For database utilities

# Async Testing
aioresponses>=0.7.4  # For mocking async HTTP requests

# Performance Testing
pytest-benchmark>=4.0.0  # For benchmarking tests
memory-profiler>=0.61.0  # For memory profiling

# Linting and Formatting (optional but recommended)
pylint>=2.17.4
black>=23.3.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.4.0

# Test Reporting
pytest-html>=3.2.0  # For HTML test reports
pytest-json-report>=1.5.0  # For JSON test reports
allure-pytest>=2.13.2  # For Allure test reports
