CREATE OR REPLACE VIEW full_category_paths AS
SELECT 
    l1.category_name AS level1_name, 
    l2.category_name AS level2_name, 
    l3.category_name AS level3_name,
    l4.category_name AS level4_name,
    l5.category_name AS level5_name,
    l6.category_name AS level6_name,
    l7.category_name AS level7_name,
    l1.category_name || ' > ' || l2.category_name || ' > ' || l3.category_name || ' > ' || l4.category_name || ' > ' || l5.category_name || ' > ' || l6.category_name || ' > ' || l7.category_name AS full_path
FROM categories_level1 l1
LEFT JOIN categories_level2 l2 ON l1.level1_id = l2.level1_parent
LEFT JOIN categories_level3 l3 ON l2.level2_id = l3.level2_parent
LEFT JOIN categories_level4 l4 ON l3.level3_id = l4.level3_parent
LEFT JOIN categories_level5 l5 ON l4.level4_id = l5.level4_parent
LEFT JOIN categories_level6 l6 ON l5.level5_id = l6.level5_parent
LEFT JOIN categories_level7 l7 ON l6.level6_id = l7.level6_parent;
