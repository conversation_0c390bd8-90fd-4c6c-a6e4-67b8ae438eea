"""
Data schemas for the CrewAI-based category mapping component.

This module defines Pydantic models for input and output data structures
used in the product category mapping workflow.
"""

from typing import List, Optional
from pydantic import BaseModel, Field, field_validator, ConfigDict


class ProductMappingRequest(BaseModel):
    """
    Input schema for product mapping requests.
    
    This represents the product data that will be sent to the CrewAI
    agents for category mapping.
    """
    product_id: int = Field(..., description="Unique identifier for the product")
    title: str = Field(..., description="Product title/name")
    description: str = Field(..., description="Product description")
    url: str = Field(..., description="Product URL")
    breadcrumbs: Optional[List[str]] = Field(
        None, 
        description="Category breadcrumbs from the source website (prioritized)"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "product_id": 123,
                "title": "ASDA Garden Scoop",
                "description": "This garden scoop is ideal for many tasks both in the garden and around your home",
                "url": "https://groceries.asda.com/product/garden-tools-accessories/asda-garden-scoop/1000003352998",
                "breadcrumbs": [
                    "Home & Entertainment", 
                    "Garden & Outdoor", 
                    "Gardening", 
                    "Garden Tools & Accessories"
                ]
            }
        }
    )

    @field_validator('breadcrumbs')
    @classmethod
    def validate_breadcrumbs(cls, v):
        """Ensure breadcrumbs list is not empty if provided."""
        if v is not None and len(v) == 0:
            return None
        return v


class ProductMappingResponse(BaseModel):
    """
    Output schema for product mapping responses.
    
    This represents the final category mapping result with up to 7 levels
    of categorization. Levels 6 and 7 are nullable as specified in PRD.
    """
    product_id: int = Field(..., description="Unique identifier for the product")
    level_1: Optional[str] = Field(None, description="Top-level category")
    level_2: Optional[str] = Field(None, description="Second-level category")
    level_3: Optional[str] = Field(None, description="Third-level category")
    level_4: Optional[str] = Field(None, description="Fourth-level category")
    level_5: Optional[str] = Field(None, description="Fifth-level category")
    level_6: Optional[str] = Field(None, description="Sixth-level category (nullable)")
    level_7: Optional[str] = Field(None, description="Seventh-level category (nullable)")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "product_id": 123,
                "level_1": "Home & Entertainment",
                "level_2": "Garden & Outdoor",
                "level_3": "Gardening",
                "level_4": "Garden Tools & Accessories",
                "level_5": "Garden Tools",
                "level_6": None,
                "level_7": None
            }
        }
    )

    @field_validator('level_1')
    @classmethod
    def validate_at_least_one_level(cls, v):
        """Ensure at least level_1 is provided."""
        if not v:
            raise ValueError("At least level_1 must be provided for a valid mapping")
        return v

    def to_category_dict(self) -> dict:
        """Convert to a dictionary containing only non-null category levels."""
        result = {"product_id": self.product_id}
        for level in range(1, 8):
            level_value = getattr(self, f'level_{level}')
            if level_value is not None:
                result[f'level_{level}'] = level_value
        return result


class ValidationResponse(BaseModel):
    """
    Schema for validation agent responses.
    
    This represents the output from the Validation Agent when it reviews
    the Mapper Agent's proposed category mapping.
    """
    status: str = Field(..., description="Validation status: 'approved' or 'rejected'")
    confidence_score: float = Field(
        ..., 
        ge=0.0, 
        le=1.0, 
        description="Confidence score between 0.0 and 1.0"
    )
    corrected_path: Optional[ProductMappingResponse] = Field(
        None, 
        description="Corrected category path if status is 'rejected'"
    )
    reason: Optional[str] = Field(
        None, 
        description="Explanation for approval/rejection"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example_approved": {
                "status": "approved",
                "confidence_score": 0.92,
                "corrected_path": None,
                "reason": "Category mapping is accurate and well-aligned with product description"
            },
            "example_rejected": {
                "status": "rejected",
                "confidence_score": 0.34,
                "corrected_path": {
                    "product_id": 123,
                    "level_1": "Home & Garden",
                    "level_2": "Gardening",
                    "level_3": "Garden Tools",
                    "level_4": "Hand Tools",
                    "level_5": "Scoops & Spades",
                    "level_6": None,
                    "level_7": None
                },
                "reason": "Original mapping was too generic; corrected to more specific garden tool category"
            }
        }
    )

    @field_validator('status')
    @classmethod
    def validate_status(cls, v):
        """Ensure status is either 'approved' or 'rejected'."""
        if v not in ['approved', 'rejected']:
            raise ValueError("Status must be either 'approved' or 'rejected'")
        return v


class MappingError(BaseModel):
    """
    Schema for mapping error responses.
    
    Used when the mapping process fails due to agent errors,
    tool malfunctions, or invalid outputs.
    """
    product_id: int = Field(..., description="Unique identifier for the product")
    error_type: str = Field(..., description="Type of error that occurred")
    error_message: str = Field(..., description="Detailed error message")
    timestamp: str = Field(..., description="ISO timestamp when error occurred")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "product_id": 123,
                "error_type": "agent_failure",
                "error_message": "Mapper Agent failed to generate valid category path",
                "timestamp": "2024-01-01T12:00:00Z"
            }
        }
    )