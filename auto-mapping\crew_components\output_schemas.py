"""Pydantic output schemas for category mapping agent task results to enforce strict JSON outputs."""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class CategoryMapping(BaseModel):
    """Schema for category mapping result from mapper agent."""
    level_1: str = Field("", description="Level 1 category name")
    level_2: str = Field("", description="Level 2 category name") 
    level_3: str = Field("", description="Level 3 category name")
    level_4: str = Field("", description="Level 4 category name")
    level_5: str = Field("", description="Level 5 category name")
    level_6: str = Field("", description="Level 6 category name")
    level_7: str = Field("", description="Level 7 category name")


class ValidationResult(BaseModel):
    """Schema for validation result from validation agent."""
    status: str = Field(..., description="Validation status: 'approved' or 'rejected'")
    confidence_score: float = Field(..., description="Confidence score between 0.0 and 1.0")
    corrected_path: Optional[List[str]] = Field(None, description="Corrected category path if status is rejected. Array of 7 strings for levels 1-7.")
