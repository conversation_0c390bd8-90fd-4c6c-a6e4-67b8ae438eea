# CrewAI Agents Documentation

## 🤖 Agent Overview

The Category Mapping System uses two specialized CrewAI agents working in sequence to ensure accurate and validated product categorization.

## 📋 Agent Architecture

```
Product Input
     ↓
┌─────────────────┐    ┌──────────────────┐
│  Mapper Agent   │ →  │ Validation Agent │
│                 │    │                  │
│ • Analyzes data │    │ • Reviews mapping│
│ • Uses tools    │    │ • Validates path │
│ • Maps category │    │ • Provides QA    │
└─────────────────┘    └──────────────────┘
     ↓                          ↓
Initial Mapping            Final Validated
                              Mapping
```

## 🎯 Mapper Agent

### Role & Responsibilities
- **Primary Role:** Product Category Mapper
- **Goal:** Accurately map products to a 7-level category hierarchy
- **Expertise:** Product categorization with multi-level hierarchy understanding

### Agent Configuration
```python
mapper_agent = Agent(
    role='Product Category Mapper',
    goal='Accurately map products to a 7-level category hierarchy based on product information.',
    backstory=(
        'You are an expert in product categorization. Your task is to analyze product data, '
        'including names, descriptions, and any available breadcrumbs or URL paths, to determine the most '
        'fitting category for each product within a complex, multi-level hierarchy. '
        'You prioritize clear signals like breadcrumbs to work efficiently and reduce costs.'
    ),
    tools=[CategoryHybridSearchTool],  # Assigned dynamically
    allow_delegation=False,
    verbose=True
)
```

### Input Processing Strategy
1. **Priority Order:**
   - Breadcrumbs (highest priority)
   - Product name + description
   - URL paths and metadata
   - Product information fallback

2. **Tool Usage:**
   - Uses CategoryHybridSearchTool for database searches
   - Combines keyword and semantic search results
   - Validates category paths against hierarchy

### Output Format
```json
{
  "level_1": "Grocery & Gourmet Foods",
  "level_2": "Beverages", 
  "level_3": "Coffee",
  "level_4": "Whole Bean Coffee",
  "level_5": "Arabica",
  "level_6": "Medium Roast",
  "level_7": "Premium"
}
```

### Performance Characteristics
- **Execution Time:** ~10.4ms average
- **Tool Calls:** 1-2 per mapping
- **Success Rate:** >95% with proper data
- **Memory Usage:** Low (agent state only)

## ✅ Validation Agent

### Role & Responsibilities
- **Primary Role:** Mapping Validation Specialist
- **Goal:** Verify accuracy and provide corrections when necessary
- **Expertise:** Quality assurance and taxonomy validation

### Agent Configuration
```python
validation_agent = Agent(
    role='Mapping Validation Specialist',
    goal='Verify the accuracy of product category mappings and provide corrected paths when necessary.',
    backstory=(
        'You are a meticulous quality assurance expert with a deep understanding of product taxonomies. '
        'Your role is to act as a second opinion, reviewing the work of the mapping agent. '
        'You double-check if the proposed category is the best fit. If you find a mistake, you use your '
        'own search capabilities to find and propose a more accurate, valid category path.'
    ),
    tools=[CategoryHybridSearchTool],  # Assigned dynamically
    allow_delegation=False,
    verbose=True
)
```

### Validation Process
1. **Review Mapping:** Analyzes mapper's proposed category
2. **Cross-Reference:** Uses search tools to verify accuracy
3. **Quality Check:** Ensures logical fit and hierarchy consistency
4. **Correction:** Provides alternative if mapping is incorrect

### Output Format
```json
{
  "status": "approved",           // or "rejected"
  "confidence_score": 0.95,       // 0.0-1.0 scale
  "corrected_path": null          // or corrected category object
}
```

### Performance Characteristics
- **Execution Time:** ~5.5ms average
- **Validation Rate:** >90% approval rate
- **Correction Rate:** ~10% of mappings corrected
- **Memory Usage:** Low (agent state only)

## 🔄 Agent Workflow

### Sequential Processing
```python
# Task flow with context passing
map_product_task = Task(
    description='Analyze product info: {product_info}...',
    expected_output='JSON object with level_1 through level_7',
    agent=mapper_agent
)

validate_mapping_task = Task(
    description='Review mapping and validate accuracy...',
    expected_output='JSON with status, confidence_score, corrected_path',
    agent=validation_agent,
    context=[map_product_task]  # Access to mapper output
)
```

### Crew Orchestration
```python
category_mapping_crew = Crew(
    agents=[mapper_agent, validation_agent],
    tasks=[map_product_task, validate_mapping_task],
    process=Process.sequential,
    verbose=True
)

# Execute workflow
result = category_mapping_crew.kickoff(inputs={
    'product_info': json.dumps(product_data)
})
```

## 🛠️ Tool Integration

### CategoryHybridSearchTool
Both agents use the same search tool with different strategies:

```python
# Tool assignment (done in setup_agents)
mapper_agent.tools = [search_tool]
validation_agent.tools = [search_tool]
```

### Search Tool Capabilities
- **Hybrid Search:** Combines keyword + semantic search
- **FAISS Integration:** Vector similarity search
- **PostgreSQL Integration:** Keyword-based hierarchy search
- **Fallback Logic:** Graceful degradation if components unavailable

## 🎨 Prompt Engineering

### Mapper Agent Prompts
```python
# Task description template
description = (
    'Analyze the provided product information to determine the best category path. '
    'Product Info: {product_info}. '
    'Prioritize breadcrumbs and URL data if available. Use the search tool to find relevant categories '
    'from the database and construct the full 7-level path.'
)
```

### Validation Agent Prompts
```python
# Validation task template
description = (
    'Review the product information and the category path proposed by the mapper. '
    'Product Info: {product_info}. '
    'The mapper has proposed a category path. Review this path and verify if it is the most accurate '
    'and logical fit. If not, use the search tool to find a better, valid path and provide it as a correction.'
)
```

### Best Practices
1. **Clear Instructions:** Specific, actionable prompts
2. **Context Preservation:** Pass relevant data between agents
3. **Tool Guidance:** Clear instructions on when/how to use tools
4. **Output Format:** Strict JSON schema requirements
5. **Error Handling:** Graceful failure modes in prompts

## 🔍 Debugging Agents

### Common Issues
1. **Tool Access Errors:** Ensure tools are properly assigned
2. **JSON Parse Errors:** Validate output format in prompts
3. **Search Failures:** Check database connectivity and embeddings
4. **Context Issues:** Verify task context is properly configured

### Debugging Tools
```python
# Enable verbose logging
agent.verbose = True

# Profile agent execution
with profiler.profile_operation("agent_debug"):
    result = agent.execute_task(task)

# Check agent configuration
print(f"Agent tools: {len(agent.tools)}")
print(f"Agent role: {agent.role}")
print(f"Task context: {task.context}")
```

### Performance Monitoring
```python
# Monitor agent performance
@profile_function("agent_execution")
def execute_agent_with_monitoring(agent, task):
    return agent.execute_task(task)

# Track agent metrics
agent_metrics = profiler.agent_metrics
for metric in agent_metrics:
    print(f"Agent: {metric.agent_name}, Time: {metric.total_execution_time_ms}ms")
```

## 🧪 Testing Agents

### Unit Tests
```python
# Test agent configuration
def test_mapper_agent_creation():
    assert mapper_agent.role == 'Product Category Mapper'
    assert 'hierarchy' in mapper_agent.goal
    assert mapper_agent.allow_delegation is False

# Test agent with mock tools
def test_agent_with_mock_tool(mock_search_tool):
    test_agent = Agent(
        role=mapper_agent.role,
        goal=mapper_agent.goal,
        backstory=mapper_agent.backstory,
        tools=[mock_search_tool]
    )
    assert len(test_agent.tools) == 1
```

### Integration Tests
```python
# Test agent-tool integration
@patch('crew_components.agents.CategoryHybridSearchTool')
def test_agent_tool_integration(mock_tool):
    mock_tool.run.return_value = json.dumps({
        "results": [{"category_path": "Test > Category", "confidence": 0.95}]
    })
    
    # Test agent execution with tool
    # ... test code
```

## 🔧 Customization

### Adding New Agents
```python
# 1. Define agent
new_agent = Agent(
    role='Your Agent Role',
    goal='Your agent goal',
    backstory='Your agent backstory',
    tools=[],  # Tools assigned later
    allow_delegation=False,
    verbose=True
)

# 2. Create task
new_task = Task(
    description='Task description with {variables}',
    expected_output='Expected output format',
    agent=new_agent
)

# 3. Add to crew
updated_crew = Crew(
    agents=[mapper_agent, validation_agent, new_agent],
    tasks=[map_product_task, validate_mapping_task, new_task],
    process=Process.sequential
)
```

### Modifying Agent Behavior
```python
# Update agent backstory for different behavior
agent.backstory = "Updated instructions for new behavior..."

# Add custom tools
custom_tool = YourCustomTool()
agent.tools.append(custom_tool)

# Modify task prompts
task.description = "Updated task description with new requirements..."
```

## 📊 Agent Performance Optimization

### Performance Tips
1. **Tool Efficiency:** Minimize unnecessary tool calls
2. **Prompt Optimization:** Clear, concise prompts reduce processing time
3. **Context Management:** Pass only necessary context between tasks
4. **Parallel Execution:** Use ParallelAgentProcessor for batch operations
5. **Caching:** Cache agent responses for repeated queries

### Monitoring Agent Performance
```python
# Monitor individual agent performance
agent_benchmark = analyzer.benchmark_agent_execution(test_products)
print(f"Mapper avg: {agent_benchmark['mapper_agent']['avg_time_ms']}ms")
print(f"Validator avg: {agent_benchmark['validation_agent']['avg_time_ms']}ms")
```

## 🚨 Troubleshooting

### Common Agent Issues

| Issue | Symptoms | Solution |
|-------|----------|----------|
| Tool not found | AttributeError on tool access | Verify tool assignment in setup_agents() |
| JSON parse error | Invalid JSON in agent output | Review prompt templates and output format |
| Slow execution | High latency per product | Enable parallel processing or optimize prompts |
| Low accuracy | Poor category mappings | Review agent backstory and search tool results |
| Memory issues | High RAM usage | Check for tool memory leaks or large context |

### Debug Commands
```bash
# Test agent configuration
python -c "from crew_components.agents import mapper_agent; print(mapper_agent.role)"

# Test tool assignment
python -c "from crew_components import setup_agents; setup_agents(None)"

# Profile agent execution
python analyze_performance.py

# Run agent tests
pytest tests/unit/test_agents.py -v
```

---

For more detailed information, see the [API Reference](api_reference.md) and [Performance Guide](performance.md).
