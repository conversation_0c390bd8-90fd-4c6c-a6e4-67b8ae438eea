"""
Hybrid Category Search Tool

Combines keyword-based search with semantic embeddings search
for better category matching. Falls back to keyword-only if
embeddings are not available.
"""

import os
from typing import List, Dict, Optional, Tuple
from .category_hierarchy_tool import CategoryHierarchyTool
from .category_embeddings_manager import CategoryEmbeddingsManager
from crewai.tools import BaseTool
from pydantic import BaseModel, Field
from typing import Type


class HybridSearchResult:
    """Result from hybrid search combining keyword and semantic scores."""
    
    def __init__(self, category: Dict, keyword_score: float = 0, semantic_score: float = 0):
        self.category = category
        self.keyword_score = keyword_score
        self.semantic_score = semantic_score
        # Weight: 60% semantic, 40% keyword (adjustable)
        self.total_score = (0.6 * semantic_score) + (0.4 * keyword_score)
    
    def __repr__(self):
        return f"<Result: {self.category.get('path', 'Unknown')} (total={self.total_score:.2f})>"


class CategoryHybridSearch:
    """
    Hybrid search combining keyword and semantic similarity.
    """
    
    def __init__(self):
        """Initialize both search methods."""
        print("Initializing Hybrid Search...")
        
        # Keyword-based search (always available)
        self.keyword_tool = CategoryHierarchyTool()
        print(f"  [OK] Keyword search ready ({len(self.keyword_tool.category_hierarchy)} categories)")
        
        # Semantic search (optional)
        self.embeddings_manager = CategoryEmbeddingsManager()
        self.semantic_available = self.embeddings_manager.embeddings is not None
        
        if self.semantic_available:
            print(f"  [OK] Semantic search ready ({self.embeddings_manager.get_statistics()['num_embeddings']} embeddings)")
        else:
            print("  [INFO] Semantic search not available (no embeddings)")
            print("        Run 'python generate_embeddings.py' to enable semantic search")
    
    def search(self, query: str, k: int = 5, use_semantic: bool = True) -> List[HybridSearchResult]:
        """
        Perform hybrid search combining keyword and semantic similarity.
        
        Args:
            query: Search query
            k: Number of results to return
            use_semantic: Whether to use semantic search if available
            
        Returns:
            List of search results sorted by combined score
        """
        results_dict = {}  # path -> HybridSearchResult
        
        # 1. Keyword search
        keyword_results = self._keyword_search(query, k=k*2)  # Get more for merging
        for cat, score in keyword_results:
            path = cat.get('full_path', cat.get('path', ''))
            results_dict[path] = HybridSearchResult(cat, keyword_score=score)
        
        # 2. Semantic search (if available and requested)
        if use_semantic and self.semantic_available:
            semantic_results = self.embeddings_manager.search_similar(query, k=k*2)
            
            for cat, score in semantic_results:
                path = cat.get('path', '')
                if path in results_dict:
                    # Update existing result with semantic score
                    results_dict[path].semantic_score = score
                else:
                    # Add new result from semantic search
                    results_dict[path] = HybridSearchResult(cat, semantic_score=score)
        
        # 3. Sort by combined score and return top k
        all_results = list(results_dict.values())
        all_results.sort(key=lambda x: x.total_score, reverse=True)
        
        return all_results[:k]
    
    def _keyword_search(self, query: str, k: int = 10) -> List[Tuple[Dict, float]]:
        """
        Perform keyword-based search.
        
        Returns:
            List of (category, normalized_score) tuples
        """
        keywords = [w.lower() for w in query.split() if len(w) > 2]
        matches = []
        
        for category in self.keyword_tool.category_hierarchy:
            score = 0
            path_lower = category['full_path'].lower()
            
            # Score based on keyword matches
            for keyword in keywords:
                if keyword in path_lower:
                    score += 1
                    # Bonus for matching higher levels
                    for level in ['level_1', 'level_2', 'level_3']:
                        if category.get(level) and keyword in category[level].lower():
                            score += 2
            
            if score > 0:
                # Normalize score (0-1 range)
                normalized_score = min(score / (len(keywords) * 3), 1.0)
                matches.append((category, normalized_score))
        
        # Sort and return top k
        matches.sort(key=lambda x: x[1], reverse=True)
        return matches[:k]
    
    def format_results(self, results: List[HybridSearchResult], query: str) -> str:
        """Format search results for display."""
        if not results:
            return f"No categories found for: {query}"
        
        output = f"Hybrid search results for: {query}\n\n"
        
        if self.semantic_available:
            output += "RESULTS (Keyword + Semantic):\n\n"
        else:
            output += "RESULTS (Keyword only - embeddings not available):\n\n"
        
        for i, result in enumerate(results, 1):
            cat = result.category
            
            # Build path
            if 'path' in cat:
                path = cat['path']
            else:
                path_parts = []
                for level in range(1, 8):
                    level_val = cat.get(f'level_{level}')
                    if level_val:
                        path_parts.append(level_val)
                path = ' > '.join(path_parts)
            
            output += f"{i}. {path}\n"
            
            # Show scores
            if self.semantic_available:
                output += f"   Scores: keyword={result.keyword_score:.2f}, "
                output += f"semantic={result.semantic_score:.2f}, "
                output += f"combined={result.total_score:.2f}\n"
            else:
                output += f"   Score: {result.keyword_score:.2f}\n"
            
            # Show product count if available
            if 'product_count' in cat:
                output += f"   Products: {cat['product_count']:,}\n"
            
            # Show category levels
            output += "   Levels:\n"
            for level_num in range(1, 8):
                level_key = f'level_{level_num}'
                level_val = cat.get(level_key) or cat.get('levels', {}).get(level_key)
                if level_val:
                    output += f"   - {level_key}: {level_val}\n"
            
            output += "\n"
        
        return output


# CrewAI Tool Wrapper
class HybridSearchInput(BaseModel):
    """Input schema for hybrid category search"""
    query: str = Field(description="The search query describing the product to categorize")


class CategoryHybridSearchTool(BaseTool):
    """CrewAI-compatible wrapper for hybrid search."""
    
    name: str = "category_hybrid_search"
    description: str = (
        "Advanced category search using both keyword matching and semantic similarity. "
        "Provides more accurate results by understanding meaning and context. "
        "Returns structured category paths with all 7 levels."
    )
    args_schema: Type[BaseModel] = HybridSearchInput
    search_tool: CategoryHybridSearch = None
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        if self.search_tool is None:
            self.search_tool = CategoryHybridSearch()
    
    def _run(self, query: str) -> str:
        """Execute the hybrid search."""

        # Ensure we have a valid string query
        if not query or not isinstance(query, str):
            return "Error: Please provide a valid search query string. Example: 'garden tools'"

        results = self.search_tool.search(query, k=5)
        return self.search_tool.format_results(results, query)


def test_hybrid_search():
    """Test the hybrid search functionality."""
    print("=" * 60)
    print("TESTING HYBRID CATEGORY SEARCH")
    print("=" * 60)
    
    # Initialize search
    search = CategoryHybridSearch()
    
    # Test queries
    test_queries = [
        "garden scoop tool outdoor",
        "fresh apple fruit produce", 
        "toothbrush dental hygiene",
        "yard maintenance equipment",  # Semantic: should match garden
        "oral care products"  # Semantic: should match dental
    ]
    
    for query in test_queries:
        print(f"\n{'='*60}")
        print(f"Query: '{query}'")
        print("-" * 60)
        
        results = search.search(query, k=3)
        formatted = search.format_results(results, query)
        print(formatted)
    
    # Show stats
    print("=" * 60)
    print("SEARCH CAPABILITIES:")
    print(f"  - Keyword search: ACTIVE ({len(search.keyword_tool.category_hierarchy)} categories)")
    if search.semantic_available:
        stats = search.embeddings_manager.get_statistics()
        print(f"  - Semantic search: ACTIVE ({stats['num_embeddings']} embeddings)")
        print(f"  - Storage: {stats['storage_size_mb']} MB")
    else:
        print("  - Semantic search: NOT AVAILABLE")
        print("    Run 'python generate_embeddings.py' to enable")
    print("=" * 60)


if __name__ == "__main__":
    test_hybrid_search()
