"""
CrewAI Components Module

This module contains all the components for the category mapping CrewAI system:
- Agents: Mapper and Validation agents
- Tasks: Product mapping and validation tasks
- Crew: The orchestration of agents and tasks
- Tools: Category search tools with keyword and semantic capabilities
- Schemas: Data transfer objects and validation schemas
"""

# Import agents
from .agents import mapper_agent, validation_agent

# Import tasks
from .tasks import map_product_task, validate_mapping_task

# Import schemas
from .schemas import (
    ProductMappingRequest,
    ProductMappingResponse,
    ValidationResponse,
    MappingError
)

# Import tools
from .tools import (
    CategoryEmbeddingsManager,
    CategorySemanticSearch,
    CategorySemanticSearchTool
)

__all__ = [
    # Agents
    'mapper_agent',
    'validation_agent',
    # Tasks
    'map_product_task',
    'validate_mapping_task',
    # Schemas
    'ProductMappingRequest',
    'ProductMappingResponse',
    'ValidationResponse',
    'MappingError',
    # Tools
    'CategoryEmbeddingsManager',
    'CategorySemanticSearch',
    'CategorySemanticSearchTool'
]
