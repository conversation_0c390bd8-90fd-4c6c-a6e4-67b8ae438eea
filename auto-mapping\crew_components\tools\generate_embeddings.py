"""
Generate Embeddings Script

This script generates embeddings for all category paths and stores them locally.
Run this once to create the embeddings, then use them for semantic search.
"""

import os
import sys
from dotenv import load_dotenv
from .category_embeddings_manager import CategoryEmbeddingsManager

load_dotenv()


def main():
    """Generate embeddings for all categories."""
    
    print("=" * 60)
    print("CATEGORY EMBEDDINGS GENERATOR")
    print("=" * 60)
    
    # Check for OpenAI API key
    if not os.getenv('OPENAI_API_KEY'):
        print("\nERROR: OpenAI API key not found!")
        print("Please set OPENAI_API_KEY in your .env file")
        return 1
    
    # Initialize manager
    manager = CategoryEmbeddingsManager()
    
    # Check current status
    stats = manager.get_statistics()
    
    if stats['embeddings_exist']:
        print(f"\nEmbeddings already exist:")
        print(f"  - Categories: {stats['num_embeddings']}")
        print(f"  - Storage: {stats['storage_size_mb']} MB")
        print(f"  - Generated: {stats['config']['generated_at'] if stats['config'] else 'Unknown'}")
        
        response = input("\nRegenerate embeddings? (y/N): ").lower()
        if response != 'y':
            print("Keeping existing embeddings.")
            return 0
    
    # Estimate cost
    print("\nEstimating cost...")
    categories = manager.load_categories_from_db()
    num_categories = len(categories)
    
    # text-embedding-3-small pricing: $0.00002 per 1K tokens
    # Estimate ~20 tokens per category path
    estimated_tokens = num_categories * 20
    estimated_cost = (estimated_tokens / 1000) * 0.00002
    
    print(f"\nWill generate embeddings for {num_categories} categories")
    print(f"Estimated tokens: {estimated_tokens:,}")
    print(f"Estimated cost: ${estimated_cost:.4f}")
    
    response = input("\nProceed with generation? (y/N): ").lower()
    if response != 'y':
        print("Generation cancelled.")
        return 0
    
    # Generate embeddings
    print("\nGenerating embeddings...")
    manager.generate_all_embeddings(force_regenerate=True)
    
    # Verify
    stats = manager.get_statistics()
    if stats['embeddings_exist']:
        print("\n" + "=" * 60)
        print("SUCCESS! Embeddings generated and saved:")
        print(f"  - Categories: {stats['num_embeddings']}")
        print(f"  - Storage: {stats['storage_size_mb']} MB")
        print(f"  - Index: {'Created' if stats['index_exists'] else 'Not created'}")
        print("=" * 60)
        return 0
    else:
        print("\nERROR: Failed to generate embeddings")
        return 1


if __name__ == "__main__":
    sys.exit(main())
